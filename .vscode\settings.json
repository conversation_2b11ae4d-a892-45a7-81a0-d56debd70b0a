{"wake.compiler.solc.remappings": [], "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.workspaceSymbols.scope": "allOpenProjects", "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/packages/*/dist": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/packages/*/dist": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": [".", "packages/core", "packages/shared-ui", "packages/auth", "packages/mall-management", "packages/shop-management", "packages/analytics", "packages/events"]}