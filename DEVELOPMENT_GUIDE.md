# MallSurf Development Guide

## 🏗️ Modular Architecture Overview

MallSurf has been restructured into a modular monorepo to enable independent development by multiple team members. Each package represents a specific domain of functionality.

## 📦 Package Assignments

### For New Team Members

#### **Junior Developer - Authentication Package** (`@mallsurf/auth`)
**Estimated Time**: 2-3 weeks
**Skills Required**: Basic React, TypeScript, JWT
**Responsibilities**:
- User registration and login
- Password management
- JWT token handling
- User profile management
- Role-based access control

**Getting Started**:
```bash
cd packages/auth
npm install
npm run dev
```

#### **Junior Developer - Events Package** (`@mallsurf/events`)
**Estimated Time**: 1-2 weeks
**Skills Required**: Basic CRUD operations, React
**Responsibilities**:
- Event creation and management
- Event calendar functionality
- Event notifications
- Simple dashboard components

**Getting Started**:
```bash
cd packages/events
npm install
npm run dev
```

#### **Mid-Level Developer - Mall Management** (`@mallsurf/mall-management`)
**Estimated Time**: 3-4 weeks
**Skills Required**: Complex business logic, database relations
**Responsibilities**:
- Mall CRUD operations
- Floor management
- Mall hours and settings
- Mall manager dashboard
- Complex data relationships

#### **Mid-Level Developer - Shop Management** (`@mallsurf/shop-management`)
**Estimated Time**: 3-4 weeks
**Skills Required**: E-commerce logic, integrations
**Responsibilities**:
- Shop CRUD operations
- Offer and promotion management
- Shop owner portal
- Inventory tracking
- Payment integrations

#### **Junior-Mid Developer - Analytics** (`@mallsurf/analytics`)
**Estimated Time**: 2-3 weeks
**Skills Required**: Data visualization, reporting
**Responsibilities**:
- Report generation
- Page view tracking
- Analytics dashboard
- Data export functionality
- Chart and graph components

## 🚀 Development Workflow

### 1. Initial Setup (One-time)
```bash
# Clone the repository
git clone <repository-url>
cd mallsurf

# Install dependencies
npm install

# Generate Prisma client
npm run db:generate
```

### 2. Package Development
```bash
# Navigate to your assigned package
cd packages/your-package-name

# Start development mode (TypeScript watch)
npm run dev

# In another terminal, run the main app
cd ../..
npm run dev
```

### 3. Testing Your Changes
```bash
# Build your package
npm run build

# Build all packages and run the app
npm run build
npm run dev
```

## 📋 Development Rules

### ✅ Do's
- Only modify files in your assigned package
- Use the `@mallsurf/core` package for shared utilities
- Follow TypeScript strict mode
- Write unit tests for your functions
- Document your API endpoints
- Use Zod for input validation

### ❌ Don'ts
- Don't modify the core package without approval
- Don't directly import from other feature packages
- Don't modify the main app structure
- Don't commit without testing
- Don't push breaking changes

## 🔧 Package Structure

Each package follows this structure:
```
packages/your-package/
├── src/
│   ├── services/          # Business logic
│   ├── components/        # React components (if needed)
│   ├── types/            # Package-specific types
│   └── index.ts          # Package exports
├── package.json
├── tsconfig.json
└── README.md
```

## 🧪 Testing

### Running Tests
```bash
# Test your package
cd packages/your-package
npm test

# Test the entire application
npm run test:all
```

### Writing Tests
- Create `__tests__` folders in your package
- Test all service functions
- Mock external dependencies
- Aim for 80%+ code coverage

## 📚 Resources

### Documentation
- [Prisma Documentation](https://www.prisma.io/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Zod Documentation](https://zod.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)

### Code Style
- Use Prettier for formatting
- Follow ESLint rules
- Use meaningful variable names
- Add JSDoc comments for public functions

## 🆘 Getting Help

1. **Check the package README** in your assigned package
2. **Review the core package** for available utilities
3. **Ask questions** in the team chat
4. **Create issues** for bugs or feature requests

## 🔄 Integration Process

1. **Develop** in your package
2. **Test** locally with the main app
3. **Create PR** for your package only
4. **Code review** by team lead
5. **Integration testing** before merge

## 📈 Progress Tracking

Each package has its own development timeline:
- **Week 1**: Setup and basic functionality
- **Week 2**: Core features implementation
- **Week 3**: Testing and refinement
- **Week 4**: Integration and documentation

Happy coding! 🚀
