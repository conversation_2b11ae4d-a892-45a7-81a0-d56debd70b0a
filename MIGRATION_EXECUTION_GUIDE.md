# 🚀 MallSurf Migration Execution Guide

## 📋 Overview
This guide provides step-by-step instructions to execute the monorepo to multi-repo migration for MallSurf.

---

## ⚠️ Pre-Migration Checklist

### Backup & Safety
- [ ] Create full backup of current repository
- [ ] Ensure all changes are committed and pushed
- [ ] Tag current state: `git tag -a pre-migration-backup -m "Backup before migration"`
- [ ] Verify all packages build successfully
- [ ] Run all tests to ensure current state is stable

### Prerequisites
- [ ] GitHub CLI installed and authenticated
- [ ] Personal access token with required scopes
- [ ] Node.js 18+ and npm/pnpm installed
- [ ] Git configured with proper credentials

---

## 🏗️ Phase 1: Repository Setup

### 1.1 Create GitHub Organization
```bash
# Create organization on GitHub web interface
# Organization name: mallsurf-packages
# Set to private by default
```

### 1.2 Create Individual Repositories
```bash
# Run the repository creation script
chmod +x scripts/create-repositories.sh
./scripts/create-repositories.sh
```

### 1.3 Configure Repository Access
Follow the instructions in `scripts/setup-repository-access.md`:
- Set organization base permissions to "No permission"
- Configure individual repository access
- Set up branch protection rules
- Create teams for different access levels

---

## 📦 Phase 2: Package Preparation

### 2.1 Update Package Configurations
All package.json files have been updated with:
- [ ] Version changed to 1.0.0
- [ ] `private: false` for publishing
- [ ] GitHub Packages registry configuration
- [ ] Repository URLs
- [ ] Publishing scripts
- [ ] Dependency versions updated

### 2.2 Create .npmrc Files
.npmrc files created for all packages with GitHub Packages configuration.

### 2.3 Set Up CI/CD Workflows
GitHub Actions workflows created for:
- [ ] Continuous Integration (testing and building)
- [ ] Automated publishing on tag creation
- [ ] Dependency updates

---

## 🔄 Phase 3: Package Migration

### 3.1 Extract Packages with Git History
```bash
# Run the migration script
chmod +x scripts/migrate-packages.sh
./scripts/migrate-packages.sh
```

This script will:
- Create backup branch
- Extract each package with full git history
- Clean up extracted repositories
- Add README and .gitignore files

### 3.2 Push to GitHub Repositories
```bash
# First, create the repositories on GitHub (if not done in Phase 1)
# Then push the extracted packages
chmod +x scripts/push-to-repos.sh
./scripts/push-to-repos.sh
```

### 3.3 Verify Repository Setup
For each repository, verify:
- [ ] Code is properly extracted
- [ ] Git history is preserved
- [ ] README and .gitignore are present
- [ ] GitHub Actions workflows are in place
- [ ] Repository access controls are correct

---

## 🏷️ Phase 4: Initial Publishing

### 4.1 Set Up GitHub Packages Authentication
```bash
# Set environment variable
export GITHUB_TOKEN=your_personal_access_token

# Verify authentication
npm whoami --registry=https://npm.pkg.github.com
```

### 4.2 Create Initial Releases
```bash
# Run the release script (publishes in dependency order)
chmod +x scripts/create-initial-releases.sh
./scripts/create-initial-releases.sh
```

This will create v1.0.0 tags for all packages in the correct order:
1. mallsurf-core (no dependencies)
2. mallsurf-auth (depends on core)
3. All other packages (depend on core + auth)

### 4.3 Verify Publishing
Check that all packages are published:
- [ ] Visit https://github.com/orgs/mallsurf-packages/packages
- [ ] Verify all 6 packages are listed
- [ ] Check version 1.0.0 is available
- [ ] Test installation: `npm install @mallsurf/core@^1.0.0`

---

## 🔧 Phase 5: Main Application Update

### 5.1 Update Main Application Dependencies
The main application package.json has been updated to:
- [ ] Remove workspace configuration
- [ ] Update dependencies to use published packages
- [ ] Simplify build scripts
- [ ] Add .npmrc for GitHub Packages

### 5.2 Test Published Package Integration
```bash
# Run the test script
chmod +x scripts/test-published-packages.sh
./scripts/test-published-packages.sh
```

This script will:
- Backup current dependencies
- Install published packages
- Build the application
- Verify everything works
- Option to keep or restore backup

### 5.3 Update CI/CD for Main Application
Update GitHub Actions workflows to:
- [ ] Use published packages instead of building from source
- [ ] Remove package building steps
- [ ] Add GitHub Packages authentication

---

## 📚 Phase 6: Documentation & Team Setup

### 6.1 Update Documentation
Created comprehensive documentation:
- [ ] `MULTI_REPO_GUIDE.md` - Complete development guide
- [ ] `TEAM_ONBOARDING_CHECKLIST.md` - Team member onboarding
- [ ] `MIGRATION_EXECUTION_GUIDE.md` - This execution guide

### 6.2 Archive Old Structure
After successful migration:
- [ ] Move `packages/` directory to `packages-archived/`
- [ ] Update main repository README
- [ ] Add migration notes to repository

### 6.3 Team Communication
- [ ] Notify team members of migration completion
- [ ] Share onboarding documentation
- [ ] Schedule training sessions if needed
- [ ] Set up support channels

---

## ✅ Post-Migration Verification

### Functional Testing
- [ ] All packages build successfully in their repositories
- [ ] Main application builds with published packages
- [ ] All tests pass
- [ ] Development workflow works end-to-end
- [ ] Publishing workflow works for all packages

### Access Control Testing
- [ ] Repository access controls work as expected
- [ ] Team members can access only assigned repositories
- [ ] Package installation works with proper authentication
- [ ] CI/CD workflows have correct permissions

### Performance Verification
- [ ] Build times are acceptable
- [ ] Package installation is fast
- [ ] Development experience is smooth
- [ ] No significant regressions

---

## 🚨 Rollback Plan

If issues arise during migration:

### Immediate Rollback
```bash
# Restore from backup branch
git checkout migration-backup-[timestamp]
git checkout -b main-restored
git branch -D main
git checkout -b main

# Restore node_modules if backed up
mv node_modules.backup node_modules
mv package-lock.json.backup package-lock.json
```

### Partial Rollback
- Keep successful repositories
- Rollback problematic packages to workspace dependencies
- Fix issues and re-migrate specific packages

---

## 📊 Success Metrics

### Technical Success
- [ ] All 6 packages successfully published
- [ ] Main application works with published packages
- [ ] CI/CD pipelines operational
- [ ] No functionality regressions
- [ ] Performance maintained or improved

### Team Success
- [ ] Team members can access assigned repositories
- [ ] Development workflow is clear and documented
- [ ] Onboarding process is streamlined
- [ ] Support and communication channels established

### Business Success
- [ ] Enhanced privacy and security achieved
- [ ] Ability to selectively share code with team members
- [ ] Scalable development structure in place
- [ ] Reduced risk of code exposure

---

## 🎉 Migration Complete!

Once all phases are completed successfully:

1. **Celebrate** - You've successfully migrated to a secure, scalable multi-repo structure!
2. **Monitor** - Keep an eye on the new workflows for the first few weeks
3. **Iterate** - Gather feedback and improve processes
4. **Scale** - Start onboarding team members to their assigned packages

Your MallSurf codebase is now perfectly positioned for secure, scalable team development! 🚀
