{"name": "mallsurf", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npm run build:packages && next build", "build:packages": "npm run build:core && npm run build:shared-ui && npm run build:auth && npm run build:mall-management && npm run build:shop-management && npm run build:analytics && npm run build:events", "build:core": "cd packages/core && npm run build", "build:shared-ui": "cd packages/shared-ui && npm run build", "build:auth": "cd packages/auth && npm run build", "build:mall-management": "cd packages/mall-management && npm run build", "build:shop-management": "cd packages/shop-management && npm run build", "build:analytics": "cd packages/analytics && npm run build", "build:events": "cd packages/events && npm run build", "start": "next start", "lint": "next lint", "db:generate": "npx prisma generate", "db:push": "npx prisma db push"}, "dependencies": {"@mallsurf/core": "workspace:*", "@mallsurf/shared-ui": "workspace:*", "@mallsurf/auth": "workspace:*", "@mallsurf/mall-management": "workspace:*", "@mallsurf/shop-management": "workspace:*", "@mallsurf/analytics": "workspace:*", "@mallsurf/events": "workspace:*", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}