# MallSurf Modular Architecture

This directory contains the modular packages that make up the MallSurf application. Each package is designed to be developed independently by different team members.

## Package Structure

### 🔧 Core Packages

#### `@mallsurf/core`
**Owner**: Lead Developer (You)
- **Purpose**: Shared utilities, types, database schema, and common functionality
- **Contents**: 
  - Database connection and Prisma client
  - API response types
  - Utility functions
  - Error handling
  - Generated Prisma types and Zod schemas

#### `@mallsurf/shared-ui`
**Owner**: UI/UX Developer
- **Purpose**: Reusable UI components
- **Contents**:
  - Radix UI components
  - Custom styled components
  - Theme provider
  - Common UI patterns

### 🔐 Feature Packages

#### `@mallsurf/auth`
**Owner**: Junior Developer
- **Purpose**: Authentication and user management
- **Contents**:
  - User registration/login services
  - JWT token management
  - Auth context and stores
  - User CRUD operations
  - Role-based access control

#### `@mallsurf/mall-management`
**Owner**: Mid-level Developer
- **Purpose**: Mall operations and management
- **Contents**:
  - Mall CRUD operations
  - Floor management
  - Mall hours management
  - Mall manager dashboard logic

#### `@mallsurf/shop-management`
**Owner**: Mid-level Developer
- **Purpose**: Shop operations and management
- **Contents**:
  - Shop CRUD operations
  - Shop hours management
  - Offer management
  - Shop owner portal logic

#### `@mallsurf/analytics`
**Owner**: Junior-Mid Developer
- **Purpose**: Reporting and analytics
- **Contents**:
  - Report generation
  - Page view tracking
  - Analytics dashboard
  - Data visualization helpers

#### `@mallsurf/events`
**Owner**: Junior Developer
- **Purpose**: Event management
- **Contents**:
  - Event CRUD operations
  - Event calendar logic
  - Event notifications

## Development Workflow

### For Package Developers

1. **Clone only your assigned package**:
   ```bash
   # Each developer gets access to only their package + core
   git sparse-checkout set packages/core packages/your-package
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Develop your package**:
   ```bash
   cd packages/your-package
   npm run dev  # Watch mode for TypeScript compilation
   ```

4. **Build your package**:
   ```bash
   npm run build
   ```

### For Integration

1. **Install all packages**:
   ```bash
   npm install
   ```

2. **Build all packages**:
   ```bash
   npm run build:packages
   ```

3. **Run the main application**:
   ```bash
   npm run dev
   ```

## Package Dependencies

```
@mallsurf/core (foundation)
├── @mallsurf/shared-ui
├── @mallsurf/auth
├── @mallsurf/mall-management → depends on @mallsurf/auth
├── @mallsurf/shop-management → depends on @mallsurf/auth
├── @mallsurf/analytics → depends on @mallsurf/auth
└── @mallsurf/events → depends on @mallsurf/auth
```

## Benefits

✅ **Independent Development**: Each developer works on their module without seeing others' code
✅ **Clear Boundaries**: Well-defined interfaces between modules
✅ **Scalable**: Easy to add new developers to specific modules
✅ **Maintainable**: Easier to debug and maintain individual modules
✅ **Secure**: Developers only have access to their assigned module

## Getting Started

1. Assign packages to team members based on their skill level
2. Set up repository access controls for each package
3. Provide package-specific documentation and onboarding
4. Use the main application for integration testing
