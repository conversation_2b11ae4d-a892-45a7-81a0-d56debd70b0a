import { PageView, ApiResponse } from "@mallsurf/core";
export declare const createPageView: (input: {
    page: string;
    title?: string;
    referrer?: string;
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    duration?: number;
}, userId?: string) => Promise<ApiResponse<{
    message: string;
}>>;
export declare const getPageViews: (page: number, limit: number, userId?: string, viewedAtFrom?: Date, viewedAtTo?: Date) => Promise<ApiResponse<PageView[]>>;
