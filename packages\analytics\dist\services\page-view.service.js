"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPageViews = exports.createPageView = void 0;
const core_1 = require("@mallsurf/core");
const createPageView = async (input, userId) => {
    await core_1.prisma.pageView.create({
        data: {
            ...input,
            userId,
        },
    });
    return {
        success: true,
        data: { message: "Page view created" },
    };
};
exports.createPageView = createPageView;
const getPageViews = async (page, limit, userId, viewedAtFrom, viewedAtTo) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const pageViews = await core_1.prisma.pageView.findMany({
        skip,
        take,
        where: {
            ...(userId && { userId }),
            ...(viewedAtFrom && { viewedAt: { gte: viewedAtFrom } }),
            ...(viewedAtTo && { viewedAt: { lte: viewedAtTo } }),
        }
    });
    const total = await core_1.prisma.pageView.count({
        where: {
            ...(userId && { userId }),
            ...(viewedAtFrom && { viewedAt: { gte: viewedAtFrom } }),
            ...(viewedAtTo && { viewedAt: { lte: viewedAtTo } }),
        }
    });
    return {
        success: true,
        data: pageViews,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getPageViews = getPageViews;
