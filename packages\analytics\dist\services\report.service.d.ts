import { Report, ReportType, ApiResponse } from "@mallsurf/core";
export declare const getReports: (page: number, limit: number, type?: ReportType, dateFrom?: Date, dateTo?: Date) => Promise<ApiResponse<Report[]>>;
export declare const getReport: (reportId: string) => Promise<ApiResponse<Report | null>>;
export declare const createReport: (input: {
    title: string;
    description?: string;
    type: ReportType;
    data: never;
    filters?: never;
    dateFrom: Date;
    dateTo: Date;
}, userId: string) => Promise<ApiResponse<Report>>;
