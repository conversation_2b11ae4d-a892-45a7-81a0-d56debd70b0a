"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createReport = exports.getReport = exports.getReports = void 0;
const core_1 = require("@mallsurf/core");
const getReports = async (page, limit, type, dateFrom, dateTo) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const reports = await core_1.prisma.report.findMany({
        skip,
        take,
        where: {
            ...(type && { type }),
            ...(dateFrom && { dateFrom: { gte: dateFrom } }),
            ...(dateTo && { dateTo: { lte: dateTo } }),
        }
    });
    const total = await core_1.prisma.report.count({
        where: {
            ...(type && { type }),
            ...(dateFrom && { dateFrom: { gte: dateFrom } }),
            ...(dateTo && { dateTo: { lte: dateTo } }),
        }
    });
    return {
        success: true,
        data: reports,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getReports = getReports;
const getReport = async (reportId) => {
    const report = await core_1.prisma.report.findUnique({
        where: { id: reportId },
    });
    return {
        success: true,
        data: report,
    };
};
exports.getReport = getReport;
const createReport = async (input, userId) => {
    const report = await core_1.prisma.report.create({
        data: {
            ...input,
            generatedBy: userId,
        },
    });
    return {
        success: true,
        data: report,
    };
};
exports.createReport = createReport;
