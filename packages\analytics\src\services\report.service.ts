import { Report, ReportType, prisma, ApiResponse } from "@mallsurf/core"

export const getReports = async (
    page: number,
    limit: number,
    type?: ReportType,
    dateFrom?: Date,
    dateTo?: Date
): Promise<ApiResponse<Report[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const reports = await prisma.report.findMany({
        skip,
        take,
        where: {
            ...(type && { type }),
            ...(dateFrom && { dateFrom: { gte: dateFrom } }),
            ...(dateTo && { dateTo: { lte: dateTo } }),
        }
    });

    const total = await prisma.report.count({
        where: {
            ...(type && { type }),
            ...(dateFrom && { dateFrom: { gte: dateFrom } }),
            ...(dateTo && { dateTo: { lte: dateTo } }),
        }
    });

    return {
        success: true,
        data: reports,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}

export const getReport = async (reportId: string): Promise<ApiResponse<Report | null>> => {
    const report = await prisma.report.findUnique({
        where: { id: reportId },
    });

    return {
        success: true,
        data: report,
    };
}

export const createReport = async (
    input: { title: string, description?: string, type: ReportType, data: never, filters?: never, dateFrom: Date, dateTo: Date },
    userId: string
): Promise<ApiResponse<Report>> => {
    const report = await prisma.report.create({
        data: {
            ...input,
            generatedBy: userId,
        },
    });

    return {
        success: true,
        data: report,
    };
}
