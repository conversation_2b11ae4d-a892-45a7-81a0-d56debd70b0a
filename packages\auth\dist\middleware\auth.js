import jwt from 'jsonwebtoken';
import { ApiError } from '@mallsurf/core';
export const authMiddleware = async (req) => {
    try {
        const cookie = req.cookies.get('accessToken');
        if (!cookie?.value) {
            throw new ApiError(401, 'Unauthorized - No token provided');
        }
        const token = cookie.value;
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        return decoded;
    }
    catch {
        throw new ApiError(401, 'Unauthorized - Invalid token');
    }
};
