import { User, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const ACCESS_TOKEN_EXPIRY = "15m";
export declare const REFRESH_TOKEN_EXPIRY = "7d";
export declare const RegisterInputSchema: z.ZodObject<{
    name: z.ZodString;
    email: z.ZodString;
    password: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    role: z.ZodNativeEnum<any>;
}, "strict", z.ZodTypeAny, {
    [x: string]: any;
    name?: unknown;
    email?: unknown;
    password?: unknown;
    phone?: unknown;
    avatar?: unknown;
    role?: unknown;
}, {
    [x: string]: any;
    name?: unknown;
    email?: unknown;
    password?: unknown;
    phone?: unknown;
    avatar?: unknown;
    role?: unknown;
}>;
export type RegisterInput = z.infer<typeof RegisterInputSchema>;
export declare const LoginInputSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export type LoginInput = z.infer<typeof LoginInputSchema>;
export declare const registerUser: (input: RegisterInput) => Promise<ApiResponse<User | null>>;
export declare const loginUser: (input: LoginInput) => Promise<ApiResponse<{
    user: User;
    accessToken: string;
} | null>>;
export declare const generateAccessToken: (user: User) => string;
