import { User, UserRole, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const UpdateProfileInputSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    email: z.<PERSON>odOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    preferences: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
    avatar?: string | undefined;
    preferences?: any;
}, {
    name?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
    avatar?: string | undefined;
    preferences?: any;
}>;
export type UpdateProfileInput = z.infer<typeof UpdateProfileInputSchema>;
export declare const updateProfile: (input: UpdateProfileInput, userId: string) => Promise<ApiResponse<{
    user: User;
    accessToken: string;
} | null>>;
export declare const getUser: (userId: string) => Promise<ApiResponse<Omit<User, "password"> | null>>;
export declare const deleteUser: (userId: string) => Promise<ApiResponse<{
    message: string;
}>>;
export declare const updateUser: (name: string, role: UserRole, isActive: boolean, userId: string) => Promise<ApiResponse<User | null>>;
export declare const getUsers: (page: number, limit: number, role?: UserRole, isActive?: boolean, search?: string) => Promise<ApiResponse<User[]>>;
