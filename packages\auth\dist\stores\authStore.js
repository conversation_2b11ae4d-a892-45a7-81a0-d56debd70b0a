import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@mallsurf/core';
export const useAuthStore = create()(persist((set, get) => ({
    // Initial state
    user: null,
    accessToken: null,
    isAuthenticated: false,
    loading: false,
    error: null,
    // Actions
    login: async (email, password) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.post('/auth/login', { email, password });
            if (response.data.success) {
                const { user, accessToken } = response.data.data;
                // Set authorization header for future requests
                apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
                set({
                    user,
                    accessToken,
                    isAuthenticated: true,
                    loading: false,
                    error: null,
                });
            }
            else {
                throw new Error(response.data.error || 'Lo<PERSON> failed');
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        }
        catch (error) {
            set({
                loading: false,
                error: error.response?.data?.error || error.message || 'Login failed',
            });
            throw error;
        }
    },
    register: async (data) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.post('/auth/register', data);
            if (response.data.success) {
                // After successful registration, automatically log in
                await get().login(data.email, data.password);
            }
            else {
                throw new Error(response.data.error || 'Registration failed');
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        }
        catch (error) {
            set({
                loading: false,
                error: error.response?.data?.error || error.message || 'Registration failed',
            });
            throw error;
        }
    },
    logout: () => {
        // Remove authorization header
        delete apiClient.defaults.headers.common['Authorization'];
        set({
            user: null,
            accessToken: null,
            isAuthenticated: false,
            error: null,
        });
    },
    checkAuth: async () => {
        try {
            const { accessToken } = get();
            if (!accessToken) {
                set({ loading: false });
                return;
            }
            set({ loading: true });
            // Set authorization header
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
            const response = await apiClient.get('/auth/me');
            if (response.data.success) {
                set({
                    user: response.data.data,
                    isAuthenticated: true,
                    loading: false,
                });
            }
            else {
                // Token is invalid, clear auth state
                get().logout();
                set({ loading: false });
            }
        }
        catch (error) {
            console.error('Auth check failed:', error);
            get().logout();
            set({ loading: false });
        }
    },
    clearError: () => set({ error: null }),
    hasRole: (roles) => {
        const { user } = get();
        return user ? roles.includes(user.role) : false;
    },
    setLoading: (loading) => set({ loading }),
}), {
    name: 'auth-storage',
    partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        isAuthenticated: state.isAuthenticated,
    }),
}));
