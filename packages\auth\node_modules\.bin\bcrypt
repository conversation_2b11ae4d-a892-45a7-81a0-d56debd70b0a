#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/bin/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/bin/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/bcryptjs@3.0.2/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../bcryptjs/bin/bcrypt" "$@"
else
  exec node  "$basedir/../bcryptjs/bin/bcrypt" "$@"
fi
