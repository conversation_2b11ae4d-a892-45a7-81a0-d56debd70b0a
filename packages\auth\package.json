{"name": "@mallsurf/auth", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@mallsurf/core": "workspace:*", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "axios": "^1.10.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}