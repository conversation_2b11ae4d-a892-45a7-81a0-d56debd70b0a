import { ApiError } from '@mallsurf/core';
import { ZodSchema, ZodError } from 'zod';

export const validate = async (schema: ZodSchema, data: unknown) => {
    try {
        return await schema.parseAsync(data);
    } catch (error: unknown) {
        if (error instanceof ZodError) {
            throw new ApiError(400, `Validation error: ${error.errors[0].message}`);
        }
        throw error;
    }
}