import { User, UserRole, prisma, ApiResponse } from "@mallsurf/core"
import { z } from "zod"
import { generateAccessToken } from "./auth.service"

export const UpdateProfileInputSchema = z.object({
    name: z.string().min(1).optional(),
    email: z.string().email().optional(),
    phone: z.string().min(10).max(15).optional(),
    avatar: z.string().url().optional(),
    preferences: z.any().optional(),
})
export type UpdateProfileInput = z.infer<typeof UpdateProfileInputSchema>

export const updateProfile = async (input: UpdateProfileInput, userId: string): Promise<ApiResponse<{ user: User, accessToken: string } | null>> => {
    const { name, email, phone, avatar, preferences } = input;

    const user = await prisma.user.update({
        where: { id: userId },
        data: {
            name,
            email,
            phone,
            avatar,
            preferences,
        }
    });

    const accessToken = generateAccessToken(user)

    return {
        success: true,
        data: { user, accessToken },
    };
}

export const getUser = async (userId: string): Promise<ApiResponse<Omit<User, "password"> | null>> => {
    const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            avatar: true,
            preferences: true,
            role: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
        },
    });

    return {
        success: true,
        data: user,
    };
};

export const deleteUser = async (userId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.user.delete({
        where: { id: userId },
    });

    return {
        success: true,
        data: { message: "User deleted" },
    };
}

export const updateUser = async (name: string, role: UserRole, isActive: boolean, userId: string): Promise<ApiResponse<User | null>> => {
    const user = await prisma.user.update({
        where: { id: userId },
        data: {
            name,
            role,
            isActive,
        }
    });

    return {
        success: true,
        data: user,
    };
}


export const getUsers = async (page: number, limit: number, role?: UserRole, isActive?: boolean, search?: string): Promise<ApiResponse<User[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const users = await prisma.user.findMany({
        skip,
        take,
        where: {
            ...(role && { role }),
            ...(isActive != undefined && { isActive }),
            name: {
                contains: search,
            },
            email: {
                contains: search,
            },
        }
    });

    const total = await prisma.user.count({
        where: {
            ...(role && { role }),
            ...(isActive != undefined && { isActive }),
            name: {
                contains: search,
            },
            email: {
                contains: search,
            },
        }
    });

    return {
        success: true,
        data: users,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}