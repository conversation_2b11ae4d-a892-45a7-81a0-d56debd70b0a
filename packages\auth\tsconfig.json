{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "declaration": true, "outDir": "./dist", "rootDir": "./src", "esModuleInterop": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}