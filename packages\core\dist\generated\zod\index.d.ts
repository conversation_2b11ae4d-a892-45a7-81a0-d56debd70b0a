import { z } from 'zod';
import { Prisma } from '../prisma';
export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;
export declare const transformJsonNull: (v?: NullableJsonInput) => string | number | true | Prisma.JsonObject | Prisma.JsonArray | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;
export declare const JsonValueSchema: z.ZodType<Prisma.JsonValue>;
export type JsonValueType = z.infer<typeof JsonValueSchema>;
export declare const NullableJsonValue: z.ZodEffects<z.ZodNullable<z.ZodUnion<[z.ZodType<Prisma.JsonValue, z.ZodTypeDef, Prisma.JsonValue>, z.<PERSON>al<"DbNull">, z.Zod<PERSON>iteral<"JsonNull">]>>, string | number | true | Prisma.JsonObject | Prisma.JsonArray | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull, Prisma.JsonValue>;
export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;
export declare const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue>;
export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;
export declare const TransactionIsolationLevelSchema: z.ZodEnum<["ReadUncommitted", "ReadCommitted", "RepeatableRead", "Serializable"]>;
export declare const UserScalarFieldEnumSchema: z.ZodEnum<["id", "name", "email", "password", "phone", "avatar", "role", "isActive", "lastLoginAt", "preferences", "createdAt", "updatedAt"]>;
export declare const PageViewScalarFieldEnumSchema: z.ZodEnum<["id", "page", "title", "referrer", "userAgent", "ipAddress", "sessionId", "duration", "viewedAt", "createdAt", "updatedAt", "userId"]>;
export declare const ReportScalarFieldEnumSchema: z.ZodEnum<["id", "title", "description", "type", "data", "filters", "dateFrom", "dateTo", "createdAt", "updatedAt", "generatedBy"]>;
export declare const MallScalarFieldEnumSchema: z.ZodEnum<["id", "name", "slug", "description", "address", "city", "phone", "email", "images", "amenities", "logo", "website", "rating", "parkingAvailable", "parkingType", "isActive", "createdAt", "updatedAt", "userId"]>;
export declare const MallHourScalarFieldEnumSchema: z.ZodEnum<["id", "day", "open", "close", "isClosed", "createdAt", "updatedAt", "mallId"]>;
export declare const EventScalarFieldEnumSchema: z.ZodEnum<["id", "title", "description", "date", "endDate", "image", "location", "createdAt", "updatedAt", "mallId", "createdBy"]>;
export declare const OfferScalarFieldEnumSchema: z.ZodEnum<["id", "title", "description", "validFrom", "validTo", "terms", "createdAt", "updatedAt", "mallId", "createdBy"]>;
export declare const ShopOfferScalarFieldEnumSchema: z.ZodEnum<["shopId", "offerId"]>;
export declare const FloorScalarFieldEnumSchema: z.ZodEnum<["id", "number", "name", "mapImage", "categories", "createdAt", "updatedAt", "mallId"]>;
export declare const ShopScalarFieldEnumSchema: z.ZodEnum<["id", "name", "description", "category", "phone", "images", "email", "website", "coordinates", "logo", "isActive", "createdAt", "updatedAt", "mallId", "floorId", "userId"]>;
export declare const ShopHourScalarFieldEnumSchema: z.ZodEnum<["id", "day", "open", "close", "isClosed", "createdAt", "updatedAt", "shopId"]>;
export declare const SortOrderSchema: z.ZodEnum<["asc", "desc"]>;
export declare const NullableJsonNullValueInputSchema: z.ZodEffects<z.ZodEnum<["DbNull", "JsonNull"]>, Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull, "JsonNull" | "DbNull">;
export declare const JsonNullValueInputSchema: z.ZodEffects<z.ZodEnum<["JsonNull"]>, Prisma.NullTypes.JsonNull, "JsonNull">;
export declare const QueryModeSchema: z.ZodEnum<["default", "insensitive"]>;
export declare const JsonNullValueFilterSchema: z.ZodEffects<z.ZodEnum<["DbNull", "JsonNull", "AnyNull"]>, Prisma.NullTypes.JsonNull | Prisma.NullTypes.AnyNull, "JsonNull" | "DbNull" | "AnyNull">;
export declare const NullsOrderSchema: z.ZodEnum<["first", "last"]>;
export declare const ParkingTypeSchema: z.ZodEnum<["free", "paid", "valet"]>;
export type ParkingTypeType = `${z.infer<typeof ParkingTypeSchema>}`;
export declare const UserRoleSchema: z.ZodEnum<["admin", "mall_manager", "shop_owner", "user"]>;
export type UserRoleType = `${z.infer<typeof UserRoleSchema>}`;
export declare const ReportTypeSchema: z.ZodEnum<["MALL_ANALYTICS", "SHOP_ANALYTICS", "USER_ANALYTICS", "PAGE_VIEW_ANALYTICS"]>;
export type ReportTypeType = `${z.infer<typeof ReportTypeSchema>}`;
export declare const UserSchema: z.ZodObject<{
    role: z.ZodEnum<["admin", "mall_manager", "shop_owner", "user"]>;
    id: z.ZodString;
    name: z.ZodString;
    email: z.ZodString;
    password: z.ZodString;
    phone: z.ZodNullable<z.ZodString>;
    avatar: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    lastLoginAt: z.ZodNullable<z.ZodDate>;
    preferences: z.ZodNullable<z.ZodType<Prisma.JsonValue, z.ZodTypeDef, Prisma.JsonValue>>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    email: string;
    password: string;
    phone: string | null;
    avatar: string | null;
    role: "admin" | "mall_manager" | "shop_owner" | "user";
    isActive: boolean;
    lastLoginAt: Date | null;
    preferences: Prisma.JsonValue;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    name: string;
    email: string;
    password: string;
    phone: string | null;
    avatar: string | null;
    role: "admin" | "mall_manager" | "shop_owner" | "user";
    isActive: boolean;
    lastLoginAt: Date | null;
    preferences: Prisma.JsonValue;
    createdAt: Date;
    updatedAt: Date;
}>;
export type User = z.infer<typeof UserSchema>;
export declare const PageViewSchema: z.ZodObject<{
    id: z.ZodString;
    page: z.ZodString;
    title: z.ZodNullable<z.ZodString>;
    referrer: z.ZodNullable<z.ZodString>;
    userAgent: z.ZodNullable<z.ZodString>;
    ipAddress: z.ZodNullable<z.ZodString>;
    sessionId: z.ZodNullable<z.ZodString>;
    duration: z.ZodNullable<z.ZodNumber>;
    viewedAt: z.ZodDate;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    userId: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    page: string;
    title: string | null;
    referrer: string | null;
    userAgent: string | null;
    ipAddress: string | null;
    sessionId: string | null;
    duration: number | null;
    viewedAt: Date;
    userId: string | null;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    page: string;
    title: string | null;
    referrer: string | null;
    userAgent: string | null;
    ipAddress: string | null;
    sessionId: string | null;
    duration: number | null;
    viewedAt: Date;
    userId: string | null;
}>;
export type PageView = z.infer<typeof PageViewSchema>;
export declare const ReportSchema: z.ZodObject<{
    type: z.ZodEnum<["MALL_ANALYTICS", "SHOP_ANALYTICS", "USER_ANALYTICS", "PAGE_VIEW_ANALYTICS"]>;
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodNullable<z.ZodString>;
    data: z.ZodType<Prisma.JsonValue, z.ZodTypeDef, Prisma.JsonValue>;
    filters: z.ZodNullable<z.ZodType<Prisma.JsonValue, z.ZodTypeDef, Prisma.JsonValue>>;
    dateFrom: z.ZodDate;
    dateTo: z.ZodDate;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    generatedBy: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "MALL_ANALYTICS" | "SHOP_ANALYTICS" | "USER_ANALYTICS" | "PAGE_VIEW_ANALYTICS";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string | null;
    data: Prisma.JsonValue;
    filters: Prisma.JsonValue;
    dateFrom: Date;
    dateTo: Date;
    generatedBy: string;
}, {
    type: "MALL_ANALYTICS" | "SHOP_ANALYTICS" | "USER_ANALYTICS" | "PAGE_VIEW_ANALYTICS";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string | null;
    data: Prisma.JsonValue;
    filters: Prisma.JsonValue;
    dateFrom: Date;
    dateTo: Date;
    generatedBy: string;
}>;
export type Report = z.infer<typeof ReportSchema>;
export declare const MallSchema: z.ZodObject<{
    parkingType: z.ZodNullable<z.ZodEnum<["free", "paid", "valet"]>>;
    id: z.ZodString;
    name: z.ZodString;
    slug: z.ZodString;
    description: z.ZodNullable<z.ZodString>;
    address: z.ZodNullable<z.ZodString>;
    city: z.ZodNullable<z.ZodString>;
    phone: z.ZodNullable<z.ZodString>;
    email: z.ZodNullable<z.ZodString>;
    images: z.ZodArray<z.ZodString, "many">;
    amenities: z.ZodArray<z.ZodString, "many">;
    logo: z.ZodNullable<z.ZodString>;
    website: z.ZodNullable<z.ZodString>;
    rating: z.ZodNullable<z.ZodNumber>;
    parkingAvailable: z.ZodBoolean;
    isActive: z.ZodBoolean;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    userId: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
    description: string | null;
    slug: string;
    address: string | null;
    city: string | null;
    images: string[];
    amenities: string[];
    logo: string | null;
    website: string | null;
    rating: number | null;
    parkingAvailable: boolean;
    parkingType: "free" | "paid" | "valet" | null;
}, {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
    description: string | null;
    slug: string;
    address: string | null;
    city: string | null;
    images: string[];
    amenities: string[];
    logo: string | null;
    website: string | null;
    rating: number | null;
    parkingAvailable: boolean;
    parkingType: "free" | "paid" | "valet" | null;
}>;
export type Mall = z.infer<typeof MallSchema>;
export declare const MallHourSchema: z.ZodObject<{
    id: z.ZodString;
    day: z.ZodNumber;
    open: z.ZodString;
    close: z.ZodString;
    isClosed: z.ZodBoolean;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    mallId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
    mallId: string;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
    mallId: string;
}>;
export type MallHour = z.infer<typeof MallHourSchema>;
export declare const EventSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodString;
    date: z.ZodDate;
    endDate: z.ZodDate;
    image: z.ZodNullable<z.ZodString>;
    location: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    mallId: z.ZodString;
    createdBy: z.ZodString;
}, "strip", z.ZodTypeAny, {
    date: Date;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    mallId: string;
    endDate: Date;
    image: string | null;
    location: string | null;
    createdBy: string;
}, {
    date: Date;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    mallId: string;
    endDate: Date;
    image: string | null;
    location: string | null;
    createdBy: string;
}>;
export type Event = z.infer<typeof EventSchema>;
export declare const OfferSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodString;
    validFrom: z.ZodDate;
    validTo: z.ZodDate;
    terms: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    mallId: z.ZodString;
    createdBy: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    mallId: string;
    createdBy: string;
    validFrom: Date;
    validTo: Date;
    terms: string | null;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    mallId: string;
    createdBy: string;
    validFrom: Date;
    validTo: Date;
    terms: string | null;
}>;
export type Offer = z.infer<typeof OfferSchema>;
export declare const ShopOfferSchema: z.ZodObject<{
    shopId: z.ZodString;
    offerId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    shopId: string;
    offerId: string;
}, {
    shopId: string;
    offerId: string;
}>;
export type ShopOffer = z.infer<typeof ShopOfferSchema>;
export declare const FloorSchema: z.ZodObject<{
    id: z.ZodString;
    number: z.ZodNumber;
    name: z.ZodString;
    mapImage: z.ZodNullable<z.ZodString>;
    categories: z.ZodArray<z.ZodString, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    mallId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    number: number;
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    mallId: string;
    mapImage: string | null;
    categories: string[];
}, {
    number: number;
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    mallId: string;
    mapImage: string | null;
    categories: string[];
}>;
export type Floor = z.infer<typeof FloorSchema>;
export declare const ShopSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    description: z.ZodNullable<z.ZodString>;
    category: z.ZodString;
    phone: z.ZodNullable<z.ZodString>;
    images: z.ZodArray<z.ZodString, "many">;
    email: z.ZodNullable<z.ZodString>;
    website: z.ZodNullable<z.ZodString>;
    coordinates: z.ZodNullable<z.ZodString>;
    logo: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    mallId: z.ZodString;
    floorId: z.ZodNullable<z.ZodString>;
    userId: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
    description: string | null;
    images: string[];
    logo: string | null;
    website: string | null;
    mallId: string;
    category: string;
    coordinates: string | null;
    floorId: string | null;
}, {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
    description: string | null;
    images: string[];
    logo: string | null;
    website: string | null;
    mallId: string;
    category: string;
    coordinates: string | null;
    floorId: string | null;
}>;
export type Shop = z.infer<typeof ShopSchema>;
export declare const ShopHourSchema: z.ZodObject<{
    id: z.ZodString;
    day: z.ZodNumber;
    open: z.ZodString;
    close: z.ZodString;
    isClosed: z.ZodBoolean;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    shopId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
    shopId: string;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
    shopId: string;
}>;
export type ShopHour = z.infer<typeof ShopHourSchema>;
export declare const UserIncludeSchema: z.ZodType<Prisma.UserInclude>;
export declare const UserArgsSchema: z.ZodType<Prisma.UserDefaultArgs>;
export declare const UserCountOutputTypeArgsSchema: z.ZodType<Prisma.UserCountOutputTypeDefaultArgs>;
export declare const UserCountOutputTypeSelectSchema: z.ZodType<Prisma.UserCountOutputTypeSelect>;
export declare const UserSelectSchema: z.ZodType<Prisma.UserSelect>;
export declare const PageViewIncludeSchema: z.ZodType<Prisma.PageViewInclude>;
export declare const PageViewArgsSchema: z.ZodType<Prisma.PageViewDefaultArgs>;
export declare const PageViewSelectSchema: z.ZodType<Prisma.PageViewSelect>;
export declare const ReportIncludeSchema: z.ZodType<Prisma.ReportInclude>;
export declare const ReportArgsSchema: z.ZodType<Prisma.ReportDefaultArgs>;
export declare const ReportSelectSchema: z.ZodType<Prisma.ReportSelect>;
export declare const MallIncludeSchema: z.ZodType<Prisma.MallInclude>;
export declare const MallArgsSchema: z.ZodType<Prisma.MallDefaultArgs>;
export declare const MallCountOutputTypeArgsSchema: z.ZodType<Prisma.MallCountOutputTypeDefaultArgs>;
export declare const MallCountOutputTypeSelectSchema: z.ZodType<Prisma.MallCountOutputTypeSelect>;
export declare const MallSelectSchema: z.ZodType<Prisma.MallSelect>;
export declare const MallHourIncludeSchema: z.ZodType<Prisma.MallHourInclude>;
export declare const MallHourArgsSchema: z.ZodType<Prisma.MallHourDefaultArgs>;
export declare const MallHourSelectSchema: z.ZodType<Prisma.MallHourSelect>;
export declare const EventIncludeSchema: z.ZodType<Prisma.EventInclude>;
export declare const EventArgsSchema: z.ZodType<Prisma.EventDefaultArgs>;
export declare const EventSelectSchema: z.ZodType<Prisma.EventSelect>;
export declare const OfferIncludeSchema: z.ZodType<Prisma.OfferInclude>;
export declare const OfferArgsSchema: z.ZodType<Prisma.OfferDefaultArgs>;
export declare const OfferCountOutputTypeArgsSchema: z.ZodType<Prisma.OfferCountOutputTypeDefaultArgs>;
export declare const OfferCountOutputTypeSelectSchema: z.ZodType<Prisma.OfferCountOutputTypeSelect>;
export declare const OfferSelectSchema: z.ZodType<Prisma.OfferSelect>;
export declare const ShopOfferIncludeSchema: z.ZodType<Prisma.ShopOfferInclude>;
export declare const ShopOfferArgsSchema: z.ZodType<Prisma.ShopOfferDefaultArgs>;
export declare const ShopOfferSelectSchema: z.ZodType<Prisma.ShopOfferSelect>;
export declare const FloorIncludeSchema: z.ZodType<Prisma.FloorInclude>;
export declare const FloorArgsSchema: z.ZodType<Prisma.FloorDefaultArgs>;
export declare const FloorCountOutputTypeArgsSchema: z.ZodType<Prisma.FloorCountOutputTypeDefaultArgs>;
export declare const FloorCountOutputTypeSelectSchema: z.ZodType<Prisma.FloorCountOutputTypeSelect>;
export declare const FloorSelectSchema: z.ZodType<Prisma.FloorSelect>;
export declare const ShopIncludeSchema: z.ZodType<Prisma.ShopInclude>;
export declare const ShopArgsSchema: z.ZodType<Prisma.ShopDefaultArgs>;
export declare const ShopCountOutputTypeArgsSchema: z.ZodType<Prisma.ShopCountOutputTypeDefaultArgs>;
export declare const ShopCountOutputTypeSelectSchema: z.ZodType<Prisma.ShopCountOutputTypeSelect>;
export declare const ShopSelectSchema: z.ZodType<Prisma.ShopSelect>;
export declare const ShopHourIncludeSchema: z.ZodType<Prisma.ShopHourInclude>;
export declare const ShopHourArgsSchema: z.ZodType<Prisma.ShopHourDefaultArgs>;
export declare const ShopHourSelectSchema: z.ZodType<Prisma.ShopHourSelect>;
export declare const UserWhereInputSchema: z.ZodType<Prisma.UserWhereInput>;
export declare const UserOrderByWithRelationInputSchema: z.ZodType<Prisma.UserOrderByWithRelationInput>;
export declare const UserWhereUniqueInputSchema: z.ZodType<Prisma.UserWhereUniqueInput>;
export declare const UserOrderByWithAggregationInputSchema: z.ZodType<Prisma.UserOrderByWithAggregationInput>;
export declare const UserScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.UserScalarWhereWithAggregatesInput>;
export declare const PageViewWhereInputSchema: z.ZodType<Prisma.PageViewWhereInput>;
export declare const PageViewOrderByWithRelationInputSchema: z.ZodType<Prisma.PageViewOrderByWithRelationInput>;
export declare const PageViewWhereUniqueInputSchema: z.ZodType<Prisma.PageViewWhereUniqueInput>;
export declare const PageViewOrderByWithAggregationInputSchema: z.ZodType<Prisma.PageViewOrderByWithAggregationInput>;
export declare const PageViewScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.PageViewScalarWhereWithAggregatesInput>;
export declare const ReportWhereInputSchema: z.ZodType<Prisma.ReportWhereInput>;
export declare const ReportOrderByWithRelationInputSchema: z.ZodType<Prisma.ReportOrderByWithRelationInput>;
export declare const ReportWhereUniqueInputSchema: z.ZodType<Prisma.ReportWhereUniqueInput>;
export declare const ReportOrderByWithAggregationInputSchema: z.ZodType<Prisma.ReportOrderByWithAggregationInput>;
export declare const ReportScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ReportScalarWhereWithAggregatesInput>;
export declare const MallWhereInputSchema: z.ZodType<Prisma.MallWhereInput>;
export declare const MallOrderByWithRelationInputSchema: z.ZodType<Prisma.MallOrderByWithRelationInput>;
export declare const MallWhereUniqueInputSchema: z.ZodType<Prisma.MallWhereUniqueInput>;
export declare const MallOrderByWithAggregationInputSchema: z.ZodType<Prisma.MallOrderByWithAggregationInput>;
export declare const MallScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.MallScalarWhereWithAggregatesInput>;
export declare const MallHourWhereInputSchema: z.ZodType<Prisma.MallHourWhereInput>;
export declare const MallHourOrderByWithRelationInputSchema: z.ZodType<Prisma.MallHourOrderByWithRelationInput>;
export declare const MallHourWhereUniqueInputSchema: z.ZodType<Prisma.MallHourWhereUniqueInput>;
export declare const MallHourOrderByWithAggregationInputSchema: z.ZodType<Prisma.MallHourOrderByWithAggregationInput>;
export declare const MallHourScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.MallHourScalarWhereWithAggregatesInput>;
export declare const EventWhereInputSchema: z.ZodType<Prisma.EventWhereInput>;
export declare const EventOrderByWithRelationInputSchema: z.ZodType<Prisma.EventOrderByWithRelationInput>;
export declare const EventWhereUniqueInputSchema: z.ZodType<Prisma.EventWhereUniqueInput>;
export declare const EventOrderByWithAggregationInputSchema: z.ZodType<Prisma.EventOrderByWithAggregationInput>;
export declare const EventScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.EventScalarWhereWithAggregatesInput>;
export declare const OfferWhereInputSchema: z.ZodType<Prisma.OfferWhereInput>;
export declare const OfferOrderByWithRelationInputSchema: z.ZodType<Prisma.OfferOrderByWithRelationInput>;
export declare const OfferWhereUniqueInputSchema: z.ZodType<Prisma.OfferWhereUniqueInput>;
export declare const OfferOrderByWithAggregationInputSchema: z.ZodType<Prisma.OfferOrderByWithAggregationInput>;
export declare const OfferScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.OfferScalarWhereWithAggregatesInput>;
export declare const ShopOfferWhereInputSchema: z.ZodType<Prisma.ShopOfferWhereInput>;
export declare const ShopOfferOrderByWithRelationInputSchema: z.ZodType<Prisma.ShopOfferOrderByWithRelationInput>;
export declare const ShopOfferWhereUniqueInputSchema: z.ZodType<Prisma.ShopOfferWhereUniqueInput>;
export declare const ShopOfferOrderByWithAggregationInputSchema: z.ZodType<Prisma.ShopOfferOrderByWithAggregationInput>;
export declare const ShopOfferScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ShopOfferScalarWhereWithAggregatesInput>;
export declare const FloorWhereInputSchema: z.ZodType<Prisma.FloorWhereInput>;
export declare const FloorOrderByWithRelationInputSchema: z.ZodType<Prisma.FloorOrderByWithRelationInput>;
export declare const FloorWhereUniqueInputSchema: z.ZodType<Prisma.FloorWhereUniqueInput>;
export declare const FloorOrderByWithAggregationInputSchema: z.ZodType<Prisma.FloorOrderByWithAggregationInput>;
export declare const FloorScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.FloorScalarWhereWithAggregatesInput>;
export declare const ShopWhereInputSchema: z.ZodType<Prisma.ShopWhereInput>;
export declare const ShopOrderByWithRelationInputSchema: z.ZodType<Prisma.ShopOrderByWithRelationInput>;
export declare const ShopWhereUniqueInputSchema: z.ZodType<Prisma.ShopWhereUniqueInput>;
export declare const ShopOrderByWithAggregationInputSchema: z.ZodType<Prisma.ShopOrderByWithAggregationInput>;
export declare const ShopScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ShopScalarWhereWithAggregatesInput>;
export declare const ShopHourWhereInputSchema: z.ZodType<Prisma.ShopHourWhereInput>;
export declare const ShopHourOrderByWithRelationInputSchema: z.ZodType<Prisma.ShopHourOrderByWithRelationInput>;
export declare const ShopHourWhereUniqueInputSchema: z.ZodType<Prisma.ShopHourWhereUniqueInput>;
export declare const ShopHourOrderByWithAggregationInputSchema: z.ZodType<Prisma.ShopHourOrderByWithAggregationInput>;
export declare const ShopHourScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ShopHourScalarWhereWithAggregatesInput>;
export declare const UserCreateInputSchema: z.ZodType<Prisma.UserCreateInput>;
export declare const UserUncheckedCreateInputSchema: z.ZodType<Prisma.UserUncheckedCreateInput>;
export declare const UserUpdateInputSchema: z.ZodType<Prisma.UserUpdateInput>;
export declare const UserUncheckedUpdateInputSchema: z.ZodType<Prisma.UserUncheckedUpdateInput>;
export declare const UserCreateManyInputSchema: z.ZodType<Prisma.UserCreateManyInput>;
export declare const UserUpdateManyMutationInputSchema: z.ZodType<Prisma.UserUpdateManyMutationInput>;
export declare const UserUncheckedUpdateManyInputSchema: z.ZodType<Prisma.UserUncheckedUpdateManyInput>;
export declare const PageViewCreateInputSchema: z.ZodType<Prisma.PageViewCreateInput>;
export declare const PageViewUncheckedCreateInputSchema: z.ZodType<Prisma.PageViewUncheckedCreateInput>;
export declare const PageViewUpdateInputSchema: z.ZodType<Prisma.PageViewUpdateInput>;
export declare const PageViewUncheckedUpdateInputSchema: z.ZodType<Prisma.PageViewUncheckedUpdateInput>;
export declare const PageViewCreateManyInputSchema: z.ZodType<Prisma.PageViewCreateManyInput>;
export declare const PageViewUpdateManyMutationInputSchema: z.ZodType<Prisma.PageViewUpdateManyMutationInput>;
export declare const PageViewUncheckedUpdateManyInputSchema: z.ZodType<Prisma.PageViewUncheckedUpdateManyInput>;
export declare const ReportCreateInputSchema: z.ZodType<Prisma.ReportCreateInput>;
export declare const ReportUncheckedCreateInputSchema: z.ZodType<Prisma.ReportUncheckedCreateInput>;
export declare const ReportUpdateInputSchema: z.ZodType<Prisma.ReportUpdateInput>;
export declare const ReportUncheckedUpdateInputSchema: z.ZodType<Prisma.ReportUncheckedUpdateInput>;
export declare const ReportCreateManyInputSchema: z.ZodType<Prisma.ReportCreateManyInput>;
export declare const ReportUpdateManyMutationInputSchema: z.ZodType<Prisma.ReportUpdateManyMutationInput>;
export declare const ReportUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ReportUncheckedUpdateManyInput>;
export declare const MallCreateInputSchema: z.ZodType<Prisma.MallCreateInput>;
export declare const MallUncheckedCreateInputSchema: z.ZodType<Prisma.MallUncheckedCreateInput>;
export declare const MallUpdateInputSchema: z.ZodType<Prisma.MallUpdateInput>;
export declare const MallUncheckedUpdateInputSchema: z.ZodType<Prisma.MallUncheckedUpdateInput>;
export declare const MallCreateManyInputSchema: z.ZodType<Prisma.MallCreateManyInput>;
export declare const MallUpdateManyMutationInputSchema: z.ZodType<Prisma.MallUpdateManyMutationInput>;
export declare const MallUncheckedUpdateManyInputSchema: z.ZodType<Prisma.MallUncheckedUpdateManyInput>;
export declare const MallHourCreateInputSchema: z.ZodType<Prisma.MallHourCreateInput>;
export declare const MallHourUncheckedCreateInputSchema: z.ZodType<Prisma.MallHourUncheckedCreateInput>;
export declare const MallHourUpdateInputSchema: z.ZodType<Prisma.MallHourUpdateInput>;
export declare const MallHourUncheckedUpdateInputSchema: z.ZodType<Prisma.MallHourUncheckedUpdateInput>;
export declare const MallHourCreateManyInputSchema: z.ZodType<Prisma.MallHourCreateManyInput>;
export declare const MallHourUpdateManyMutationInputSchema: z.ZodType<Prisma.MallHourUpdateManyMutationInput>;
export declare const MallHourUncheckedUpdateManyInputSchema: z.ZodType<Prisma.MallHourUncheckedUpdateManyInput>;
export declare const EventCreateInputSchema: z.ZodType<Prisma.EventCreateInput>;
export declare const EventUncheckedCreateInputSchema: z.ZodType<Prisma.EventUncheckedCreateInput>;
export declare const EventUpdateInputSchema: z.ZodType<Prisma.EventUpdateInput>;
export declare const EventUncheckedUpdateInputSchema: z.ZodType<Prisma.EventUncheckedUpdateInput>;
export declare const EventCreateManyInputSchema: z.ZodType<Prisma.EventCreateManyInput>;
export declare const EventUpdateManyMutationInputSchema: z.ZodType<Prisma.EventUpdateManyMutationInput>;
export declare const EventUncheckedUpdateManyInputSchema: z.ZodType<Prisma.EventUncheckedUpdateManyInput>;
export declare const OfferCreateInputSchema: z.ZodType<Prisma.OfferCreateInput>;
export declare const OfferUncheckedCreateInputSchema: z.ZodType<Prisma.OfferUncheckedCreateInput>;
export declare const OfferUpdateInputSchema: z.ZodType<Prisma.OfferUpdateInput>;
export declare const OfferUncheckedUpdateInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateInput>;
export declare const OfferCreateManyInputSchema: z.ZodType<Prisma.OfferCreateManyInput>;
export declare const OfferUpdateManyMutationInputSchema: z.ZodType<Prisma.OfferUpdateManyMutationInput>;
export declare const OfferUncheckedUpdateManyInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateManyInput>;
export declare const ShopOfferCreateInputSchema: z.ZodType<Prisma.ShopOfferCreateInput>;
export declare const ShopOfferUncheckedCreateInputSchema: z.ZodType<Prisma.ShopOfferUncheckedCreateInput>;
export declare const ShopOfferUpdateInputSchema: z.ZodType<Prisma.ShopOfferUpdateInput>;
export declare const ShopOfferUncheckedUpdateInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateInput>;
export declare const ShopOfferCreateManyInputSchema: z.ZodType<Prisma.ShopOfferCreateManyInput>;
export declare const ShopOfferUpdateManyMutationInputSchema: z.ZodType<Prisma.ShopOfferUpdateManyMutationInput>;
export declare const ShopOfferUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateManyInput>;
export declare const FloorCreateInputSchema: z.ZodType<Prisma.FloorCreateInput>;
export declare const FloorUncheckedCreateInputSchema: z.ZodType<Prisma.FloorUncheckedCreateInput>;
export declare const FloorUpdateInputSchema: z.ZodType<Prisma.FloorUpdateInput>;
export declare const FloorUncheckedUpdateInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateInput>;
export declare const FloorCreateManyInputSchema: z.ZodType<Prisma.FloorCreateManyInput>;
export declare const FloorUpdateManyMutationInputSchema: z.ZodType<Prisma.FloorUpdateManyMutationInput>;
export declare const FloorUncheckedUpdateManyInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateManyInput>;
export declare const ShopCreateInputSchema: z.ZodType<Prisma.ShopCreateInput>;
export declare const ShopUncheckedCreateInputSchema: z.ZodType<Prisma.ShopUncheckedCreateInput>;
export declare const ShopUpdateInputSchema: z.ZodType<Prisma.ShopUpdateInput>;
export declare const ShopUncheckedUpdateInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateInput>;
export declare const ShopCreateManyInputSchema: z.ZodType<Prisma.ShopCreateManyInput>;
export declare const ShopUpdateManyMutationInputSchema: z.ZodType<Prisma.ShopUpdateManyMutationInput>;
export declare const ShopUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyInput>;
export declare const ShopHourCreateInputSchema: z.ZodType<Prisma.ShopHourCreateInput>;
export declare const ShopHourUncheckedCreateInputSchema: z.ZodType<Prisma.ShopHourUncheckedCreateInput>;
export declare const ShopHourUpdateInputSchema: z.ZodType<Prisma.ShopHourUpdateInput>;
export declare const ShopHourUncheckedUpdateInputSchema: z.ZodType<Prisma.ShopHourUncheckedUpdateInput>;
export declare const ShopHourCreateManyInputSchema: z.ZodType<Prisma.ShopHourCreateManyInput>;
export declare const ShopHourUpdateManyMutationInputSchema: z.ZodType<Prisma.ShopHourUpdateManyMutationInput>;
export declare const ShopHourUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ShopHourUncheckedUpdateManyInput>;
export declare const StringFilterSchema: z.ZodType<Prisma.StringFilter>;
export declare const StringNullableFilterSchema: z.ZodType<Prisma.StringNullableFilter>;
export declare const EnumUserRoleFilterSchema: z.ZodType<Prisma.EnumUserRoleFilter>;
export declare const BoolFilterSchema: z.ZodType<Prisma.BoolFilter>;
export declare const DateTimeNullableFilterSchema: z.ZodType<Prisma.DateTimeNullableFilter>;
export declare const JsonNullableFilterSchema: z.ZodType<Prisma.JsonNullableFilter>;
export declare const DateTimeFilterSchema: z.ZodType<Prisma.DateTimeFilter>;
export declare const PageViewListRelationFilterSchema: z.ZodType<Prisma.PageViewListRelationFilter>;
export declare const ReportListRelationFilterSchema: z.ZodType<Prisma.ReportListRelationFilter>;
export declare const MallListRelationFilterSchema: z.ZodType<Prisma.MallListRelationFilter>;
export declare const ShopListRelationFilterSchema: z.ZodType<Prisma.ShopListRelationFilter>;
export declare const EventListRelationFilterSchema: z.ZodType<Prisma.EventListRelationFilter>;
export declare const OfferListRelationFilterSchema: z.ZodType<Prisma.OfferListRelationFilter>;
export declare const SortOrderInputSchema: z.ZodType<Prisma.SortOrderInput>;
export declare const PageViewOrderByRelationAggregateInputSchema: z.ZodType<Prisma.PageViewOrderByRelationAggregateInput>;
export declare const ReportOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ReportOrderByRelationAggregateInput>;
export declare const MallOrderByRelationAggregateInputSchema: z.ZodType<Prisma.MallOrderByRelationAggregateInput>;
export declare const ShopOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ShopOrderByRelationAggregateInput>;
export declare const EventOrderByRelationAggregateInputSchema: z.ZodType<Prisma.EventOrderByRelationAggregateInput>;
export declare const OfferOrderByRelationAggregateInputSchema: z.ZodType<Prisma.OfferOrderByRelationAggregateInput>;
export declare const UserCountOrderByAggregateInputSchema: z.ZodType<Prisma.UserCountOrderByAggregateInput>;
export declare const UserMaxOrderByAggregateInputSchema: z.ZodType<Prisma.UserMaxOrderByAggregateInput>;
export declare const UserMinOrderByAggregateInputSchema: z.ZodType<Prisma.UserMinOrderByAggregateInput>;
export declare const StringWithAggregatesFilterSchema: z.ZodType<Prisma.StringWithAggregatesFilter>;
export declare const StringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.StringNullableWithAggregatesFilter>;
export declare const EnumUserRoleWithAggregatesFilterSchema: z.ZodType<Prisma.EnumUserRoleWithAggregatesFilter>;
export declare const BoolWithAggregatesFilterSchema: z.ZodType<Prisma.BoolWithAggregatesFilter>;
export declare const DateTimeNullableWithAggregatesFilterSchema: z.ZodType<Prisma.DateTimeNullableWithAggregatesFilter>;
export declare const JsonNullableWithAggregatesFilterSchema: z.ZodType<Prisma.JsonNullableWithAggregatesFilter>;
export declare const DateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.DateTimeWithAggregatesFilter>;
export declare const IntNullableFilterSchema: z.ZodType<Prisma.IntNullableFilter>;
export declare const UserNullableScalarRelationFilterSchema: z.ZodType<Prisma.UserNullableScalarRelationFilter>;
export declare const PageViewCountOrderByAggregateInputSchema: z.ZodType<Prisma.PageViewCountOrderByAggregateInput>;
export declare const PageViewAvgOrderByAggregateInputSchema: z.ZodType<Prisma.PageViewAvgOrderByAggregateInput>;
export declare const PageViewMaxOrderByAggregateInputSchema: z.ZodType<Prisma.PageViewMaxOrderByAggregateInput>;
export declare const PageViewMinOrderByAggregateInputSchema: z.ZodType<Prisma.PageViewMinOrderByAggregateInput>;
export declare const PageViewSumOrderByAggregateInputSchema: z.ZodType<Prisma.PageViewSumOrderByAggregateInput>;
export declare const IntNullableWithAggregatesFilterSchema: z.ZodType<Prisma.IntNullableWithAggregatesFilter>;
export declare const EnumReportTypeFilterSchema: z.ZodType<Prisma.EnumReportTypeFilter>;
export declare const JsonFilterSchema: z.ZodType<Prisma.JsonFilter>;
export declare const UserScalarRelationFilterSchema: z.ZodType<Prisma.UserScalarRelationFilter>;
export declare const ReportCountOrderByAggregateInputSchema: z.ZodType<Prisma.ReportCountOrderByAggregateInput>;
export declare const ReportMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ReportMaxOrderByAggregateInput>;
export declare const ReportMinOrderByAggregateInputSchema: z.ZodType<Prisma.ReportMinOrderByAggregateInput>;
export declare const EnumReportTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumReportTypeWithAggregatesFilter>;
export declare const JsonWithAggregatesFilterSchema: z.ZodType<Prisma.JsonWithAggregatesFilter>;
export declare const StringNullableListFilterSchema: z.ZodType<Prisma.StringNullableListFilter>;
export declare const FloatNullableFilterSchema: z.ZodType<Prisma.FloatNullableFilter>;
export declare const EnumParkingTypeNullableFilterSchema: z.ZodType<Prisma.EnumParkingTypeNullableFilter>;
export declare const MallHourListRelationFilterSchema: z.ZodType<Prisma.MallHourListRelationFilter>;
export declare const FloorListRelationFilterSchema: z.ZodType<Prisma.FloorListRelationFilter>;
export declare const MallHourOrderByRelationAggregateInputSchema: z.ZodType<Prisma.MallHourOrderByRelationAggregateInput>;
export declare const FloorOrderByRelationAggregateInputSchema: z.ZodType<Prisma.FloorOrderByRelationAggregateInput>;
export declare const MallCountOrderByAggregateInputSchema: z.ZodType<Prisma.MallCountOrderByAggregateInput>;
export declare const MallAvgOrderByAggregateInputSchema: z.ZodType<Prisma.MallAvgOrderByAggregateInput>;
export declare const MallMaxOrderByAggregateInputSchema: z.ZodType<Prisma.MallMaxOrderByAggregateInput>;
export declare const MallMinOrderByAggregateInputSchema: z.ZodType<Prisma.MallMinOrderByAggregateInput>;
export declare const MallSumOrderByAggregateInputSchema: z.ZodType<Prisma.MallSumOrderByAggregateInput>;
export declare const FloatNullableWithAggregatesFilterSchema: z.ZodType<Prisma.FloatNullableWithAggregatesFilter>;
export declare const EnumParkingTypeNullableWithAggregatesFilterSchema: z.ZodType<Prisma.EnumParkingTypeNullableWithAggregatesFilter>;
export declare const IntFilterSchema: z.ZodType<Prisma.IntFilter>;
export declare const MallScalarRelationFilterSchema: z.ZodType<Prisma.MallScalarRelationFilter>;
export declare const MallHourCountOrderByAggregateInputSchema: z.ZodType<Prisma.MallHourCountOrderByAggregateInput>;
export declare const MallHourAvgOrderByAggregateInputSchema: z.ZodType<Prisma.MallHourAvgOrderByAggregateInput>;
export declare const MallHourMaxOrderByAggregateInputSchema: z.ZodType<Prisma.MallHourMaxOrderByAggregateInput>;
export declare const MallHourMinOrderByAggregateInputSchema: z.ZodType<Prisma.MallHourMinOrderByAggregateInput>;
export declare const MallHourSumOrderByAggregateInputSchema: z.ZodType<Prisma.MallHourSumOrderByAggregateInput>;
export declare const IntWithAggregatesFilterSchema: z.ZodType<Prisma.IntWithAggregatesFilter>;
export declare const EventCountOrderByAggregateInputSchema: z.ZodType<Prisma.EventCountOrderByAggregateInput>;
export declare const EventMaxOrderByAggregateInputSchema: z.ZodType<Prisma.EventMaxOrderByAggregateInput>;
export declare const EventMinOrderByAggregateInputSchema: z.ZodType<Prisma.EventMinOrderByAggregateInput>;
export declare const ShopOfferListRelationFilterSchema: z.ZodType<Prisma.ShopOfferListRelationFilter>;
export declare const ShopOfferOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ShopOfferOrderByRelationAggregateInput>;
export declare const OfferCountOrderByAggregateInputSchema: z.ZodType<Prisma.OfferCountOrderByAggregateInput>;
export declare const OfferMaxOrderByAggregateInputSchema: z.ZodType<Prisma.OfferMaxOrderByAggregateInput>;
export declare const OfferMinOrderByAggregateInputSchema: z.ZodType<Prisma.OfferMinOrderByAggregateInput>;
export declare const ShopScalarRelationFilterSchema: z.ZodType<Prisma.ShopScalarRelationFilter>;
export declare const OfferScalarRelationFilterSchema: z.ZodType<Prisma.OfferScalarRelationFilter>;
export declare const ShopOfferShopIdOfferIdCompoundUniqueInputSchema: z.ZodType<Prisma.ShopOfferShopIdOfferIdCompoundUniqueInput>;
export declare const ShopOfferCountOrderByAggregateInputSchema: z.ZodType<Prisma.ShopOfferCountOrderByAggregateInput>;
export declare const ShopOfferMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ShopOfferMaxOrderByAggregateInput>;
export declare const ShopOfferMinOrderByAggregateInputSchema: z.ZodType<Prisma.ShopOfferMinOrderByAggregateInput>;
export declare const FloorCountOrderByAggregateInputSchema: z.ZodType<Prisma.FloorCountOrderByAggregateInput>;
export declare const FloorAvgOrderByAggregateInputSchema: z.ZodType<Prisma.FloorAvgOrderByAggregateInput>;
export declare const FloorMaxOrderByAggregateInputSchema: z.ZodType<Prisma.FloorMaxOrderByAggregateInput>;
export declare const FloorMinOrderByAggregateInputSchema: z.ZodType<Prisma.FloorMinOrderByAggregateInput>;
export declare const FloorSumOrderByAggregateInputSchema: z.ZodType<Prisma.FloorSumOrderByAggregateInput>;
export declare const FloorNullableScalarRelationFilterSchema: z.ZodType<Prisma.FloorNullableScalarRelationFilter>;
export declare const ShopHourListRelationFilterSchema: z.ZodType<Prisma.ShopHourListRelationFilter>;
export declare const ShopHourOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ShopHourOrderByRelationAggregateInput>;
export declare const ShopCountOrderByAggregateInputSchema: z.ZodType<Prisma.ShopCountOrderByAggregateInput>;
export declare const ShopMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ShopMaxOrderByAggregateInput>;
export declare const ShopMinOrderByAggregateInputSchema: z.ZodType<Prisma.ShopMinOrderByAggregateInput>;
export declare const ShopHourCountOrderByAggregateInputSchema: z.ZodType<Prisma.ShopHourCountOrderByAggregateInput>;
export declare const ShopHourAvgOrderByAggregateInputSchema: z.ZodType<Prisma.ShopHourAvgOrderByAggregateInput>;
export declare const ShopHourMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ShopHourMaxOrderByAggregateInput>;
export declare const ShopHourMinOrderByAggregateInputSchema: z.ZodType<Prisma.ShopHourMinOrderByAggregateInput>;
export declare const ShopHourSumOrderByAggregateInputSchema: z.ZodType<Prisma.ShopHourSumOrderByAggregateInput>;
export declare const PageViewCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.PageViewCreateNestedManyWithoutUserInput>;
export declare const ReportCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.ReportCreateNestedManyWithoutUserInput>;
export declare const MallCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.MallCreateNestedManyWithoutUserInput>;
export declare const ShopCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.ShopCreateNestedManyWithoutUserInput>;
export declare const EventCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.EventCreateNestedManyWithoutUserInput>;
export declare const OfferCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.OfferCreateNestedManyWithoutUserInput>;
export declare const PageViewUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.PageViewUncheckedCreateNestedManyWithoutUserInput>;
export declare const ReportUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.ReportUncheckedCreateNestedManyWithoutUserInput>;
export declare const MallUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.MallUncheckedCreateNestedManyWithoutUserInput>;
export declare const ShopUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.ShopUncheckedCreateNestedManyWithoutUserInput>;
export declare const EventUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.EventUncheckedCreateNestedManyWithoutUserInput>;
export declare const OfferUncheckedCreateNestedManyWithoutUserInputSchema: z.ZodType<Prisma.OfferUncheckedCreateNestedManyWithoutUserInput>;
export declare const StringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.StringFieldUpdateOperationsInput>;
export declare const NullableStringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableStringFieldUpdateOperationsInput>;
export declare const EnumUserRoleFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumUserRoleFieldUpdateOperationsInput>;
export declare const BoolFieldUpdateOperationsInputSchema: z.ZodType<Prisma.BoolFieldUpdateOperationsInput>;
export declare const NullableDateTimeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableDateTimeFieldUpdateOperationsInput>;
export declare const DateTimeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.DateTimeFieldUpdateOperationsInput>;
export declare const PageViewUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.PageViewUpdateManyWithoutUserNestedInput>;
export declare const ReportUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.ReportUpdateManyWithoutUserNestedInput>;
export declare const MallUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.MallUpdateManyWithoutUserNestedInput>;
export declare const ShopUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.ShopUpdateManyWithoutUserNestedInput>;
export declare const EventUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.EventUpdateManyWithoutUserNestedInput>;
export declare const OfferUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.OfferUpdateManyWithoutUserNestedInput>;
export declare const PageViewUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.PageViewUncheckedUpdateManyWithoutUserNestedInput>;
export declare const ReportUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.ReportUncheckedUpdateManyWithoutUserNestedInput>;
export declare const MallUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.MallUncheckedUpdateManyWithoutUserNestedInput>;
export declare const ShopUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutUserNestedInput>;
export declare const EventUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.EventUncheckedUpdateManyWithoutUserNestedInput>;
export declare const OfferUncheckedUpdateManyWithoutUserNestedInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateManyWithoutUserNestedInput>;
export declare const UserCreateNestedOneWithoutPageViewsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutPageViewsInput>;
export declare const NullableIntFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableIntFieldUpdateOperationsInput>;
export declare const UserUpdateOneWithoutPageViewsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneWithoutPageViewsNestedInput>;
export declare const UserCreateNestedOneWithoutReportsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutReportsInput>;
export declare const EnumReportTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumReportTypeFieldUpdateOperationsInput>;
export declare const UserUpdateOneRequiredWithoutReportsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutReportsNestedInput>;
export declare const MallCreateimagesInputSchema: z.ZodType<Prisma.MallCreateimagesInput>;
export declare const MallCreateamenitiesInputSchema: z.ZodType<Prisma.MallCreateamenitiesInput>;
export declare const UserCreateNestedOneWithoutMallsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutMallsInput>;
export declare const MallHourCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.MallHourCreateNestedManyWithoutMallInput>;
export declare const EventCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.EventCreateNestedManyWithoutMallInput>;
export declare const FloorCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.FloorCreateNestedManyWithoutMallInput>;
export declare const ShopCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.ShopCreateNestedManyWithoutMallInput>;
export declare const OfferCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.OfferCreateNestedManyWithoutMallInput>;
export declare const MallHourUncheckedCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.MallHourUncheckedCreateNestedManyWithoutMallInput>;
export declare const EventUncheckedCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.EventUncheckedCreateNestedManyWithoutMallInput>;
export declare const FloorUncheckedCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.FloorUncheckedCreateNestedManyWithoutMallInput>;
export declare const ShopUncheckedCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.ShopUncheckedCreateNestedManyWithoutMallInput>;
export declare const OfferUncheckedCreateNestedManyWithoutMallInputSchema: z.ZodType<Prisma.OfferUncheckedCreateNestedManyWithoutMallInput>;
export declare const MallUpdateimagesInputSchema: z.ZodType<Prisma.MallUpdateimagesInput>;
export declare const MallUpdateamenitiesInputSchema: z.ZodType<Prisma.MallUpdateamenitiesInput>;
export declare const NullableFloatFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableFloatFieldUpdateOperationsInput>;
export declare const NullableEnumParkingTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableEnumParkingTypeFieldUpdateOperationsInput>;
export declare const UserUpdateOneWithoutMallsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneWithoutMallsNestedInput>;
export declare const MallHourUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.MallHourUpdateManyWithoutMallNestedInput>;
export declare const EventUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.EventUpdateManyWithoutMallNestedInput>;
export declare const FloorUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.FloorUpdateManyWithoutMallNestedInput>;
export declare const ShopUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.ShopUpdateManyWithoutMallNestedInput>;
export declare const OfferUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.OfferUpdateManyWithoutMallNestedInput>;
export declare const MallHourUncheckedUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.MallHourUncheckedUpdateManyWithoutMallNestedInput>;
export declare const EventUncheckedUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.EventUncheckedUpdateManyWithoutMallNestedInput>;
export declare const FloorUncheckedUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateManyWithoutMallNestedInput>;
export declare const ShopUncheckedUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutMallNestedInput>;
export declare const OfferUncheckedUpdateManyWithoutMallNestedInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateManyWithoutMallNestedInput>;
export declare const MallCreateNestedOneWithoutMallHoursInputSchema: z.ZodType<Prisma.MallCreateNestedOneWithoutMallHoursInput>;
export declare const IntFieldUpdateOperationsInputSchema: z.ZodType<Prisma.IntFieldUpdateOperationsInput>;
export declare const MallUpdateOneRequiredWithoutMallHoursNestedInputSchema: z.ZodType<Prisma.MallUpdateOneRequiredWithoutMallHoursNestedInput>;
export declare const MallCreateNestedOneWithoutEventsInputSchema: z.ZodType<Prisma.MallCreateNestedOneWithoutEventsInput>;
export declare const UserCreateNestedOneWithoutEventsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutEventsInput>;
export declare const MallUpdateOneRequiredWithoutEventsNestedInputSchema: z.ZodType<Prisma.MallUpdateOneRequiredWithoutEventsNestedInput>;
export declare const UserUpdateOneRequiredWithoutEventsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutEventsNestedInput>;
export declare const MallCreateNestedOneWithoutOffersInputSchema: z.ZodType<Prisma.MallCreateNestedOneWithoutOffersInput>;
export declare const UserCreateNestedOneWithoutOffersInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutOffersInput>;
export declare const ShopOfferCreateNestedManyWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferCreateNestedManyWithoutOfferInput>;
export declare const ShopOfferUncheckedCreateNestedManyWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUncheckedCreateNestedManyWithoutOfferInput>;
export declare const MallUpdateOneRequiredWithoutOffersNestedInputSchema: z.ZodType<Prisma.MallUpdateOneRequiredWithoutOffersNestedInput>;
export declare const UserUpdateOneRequiredWithoutOffersNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutOffersNestedInput>;
export declare const ShopOfferUpdateManyWithoutOfferNestedInputSchema: z.ZodType<Prisma.ShopOfferUpdateManyWithoutOfferNestedInput>;
export declare const ShopOfferUncheckedUpdateManyWithoutOfferNestedInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateManyWithoutOfferNestedInput>;
export declare const ShopCreateNestedOneWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopCreateNestedOneWithoutShopOffersInput>;
export declare const OfferCreateNestedOneWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferCreateNestedOneWithoutShopOffersInput>;
export declare const ShopUpdateOneRequiredWithoutShopOffersNestedInputSchema: z.ZodType<Prisma.ShopUpdateOneRequiredWithoutShopOffersNestedInput>;
export declare const OfferUpdateOneRequiredWithoutShopOffersNestedInputSchema: z.ZodType<Prisma.OfferUpdateOneRequiredWithoutShopOffersNestedInput>;
export declare const FloorCreatecategoriesInputSchema: z.ZodType<Prisma.FloorCreatecategoriesInput>;
export declare const MallCreateNestedOneWithoutFloorsInputSchema: z.ZodType<Prisma.MallCreateNestedOneWithoutFloorsInput>;
export declare const ShopCreateNestedManyWithoutFloorInputSchema: z.ZodType<Prisma.ShopCreateNestedManyWithoutFloorInput>;
export declare const ShopUncheckedCreateNestedManyWithoutFloorInputSchema: z.ZodType<Prisma.ShopUncheckedCreateNestedManyWithoutFloorInput>;
export declare const FloorUpdatecategoriesInputSchema: z.ZodType<Prisma.FloorUpdatecategoriesInput>;
export declare const MallUpdateOneRequiredWithoutFloorsNestedInputSchema: z.ZodType<Prisma.MallUpdateOneRequiredWithoutFloorsNestedInput>;
export declare const ShopUpdateManyWithoutFloorNestedInputSchema: z.ZodType<Prisma.ShopUpdateManyWithoutFloorNestedInput>;
export declare const ShopUncheckedUpdateManyWithoutFloorNestedInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutFloorNestedInput>;
export declare const ShopCreateimagesInputSchema: z.ZodType<Prisma.ShopCreateimagesInput>;
export declare const MallCreateNestedOneWithoutShopsInputSchema: z.ZodType<Prisma.MallCreateNestedOneWithoutShopsInput>;
export declare const FloorCreateNestedOneWithoutShopsInputSchema: z.ZodType<Prisma.FloorCreateNestedOneWithoutShopsInput>;
export declare const UserCreateNestedOneWithoutShopsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutShopsInput>;
export declare const ShopHourCreateNestedManyWithoutShopInputSchema: z.ZodType<Prisma.ShopHourCreateNestedManyWithoutShopInput>;
export declare const ShopOfferCreateNestedManyWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferCreateNestedManyWithoutShopInput>;
export declare const ShopHourUncheckedCreateNestedManyWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUncheckedCreateNestedManyWithoutShopInput>;
export declare const ShopOfferUncheckedCreateNestedManyWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUncheckedCreateNestedManyWithoutShopInput>;
export declare const ShopUpdateimagesInputSchema: z.ZodType<Prisma.ShopUpdateimagesInput>;
export declare const MallUpdateOneRequiredWithoutShopsNestedInputSchema: z.ZodType<Prisma.MallUpdateOneRequiredWithoutShopsNestedInput>;
export declare const FloorUpdateOneWithoutShopsNestedInputSchema: z.ZodType<Prisma.FloorUpdateOneWithoutShopsNestedInput>;
export declare const UserUpdateOneWithoutShopsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneWithoutShopsNestedInput>;
export declare const ShopHourUpdateManyWithoutShopNestedInputSchema: z.ZodType<Prisma.ShopHourUpdateManyWithoutShopNestedInput>;
export declare const ShopOfferUpdateManyWithoutShopNestedInputSchema: z.ZodType<Prisma.ShopOfferUpdateManyWithoutShopNestedInput>;
export declare const ShopHourUncheckedUpdateManyWithoutShopNestedInputSchema: z.ZodType<Prisma.ShopHourUncheckedUpdateManyWithoutShopNestedInput>;
export declare const ShopOfferUncheckedUpdateManyWithoutShopNestedInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateManyWithoutShopNestedInput>;
export declare const ShopCreateNestedOneWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopCreateNestedOneWithoutShopHoursInput>;
export declare const ShopUpdateOneRequiredWithoutShopHoursNestedInputSchema: z.ZodType<Prisma.ShopUpdateOneRequiredWithoutShopHoursNestedInput>;
export declare const NestedStringFilterSchema: z.ZodType<Prisma.NestedStringFilter>;
export declare const NestedStringNullableFilterSchema: z.ZodType<Prisma.NestedStringNullableFilter>;
export declare const NestedEnumUserRoleFilterSchema: z.ZodType<Prisma.NestedEnumUserRoleFilter>;
export declare const NestedBoolFilterSchema: z.ZodType<Prisma.NestedBoolFilter>;
export declare const NestedDateTimeNullableFilterSchema: z.ZodType<Prisma.NestedDateTimeNullableFilter>;
export declare const NestedDateTimeFilterSchema: z.ZodType<Prisma.NestedDateTimeFilter>;
export declare const NestedStringWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringWithAggregatesFilter>;
export declare const NestedIntFilterSchema: z.ZodType<Prisma.NestedIntFilter>;
export declare const NestedStringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringNullableWithAggregatesFilter>;
export declare const NestedIntNullableFilterSchema: z.ZodType<Prisma.NestedIntNullableFilter>;
export declare const NestedEnumUserRoleWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumUserRoleWithAggregatesFilter>;
export declare const NestedBoolWithAggregatesFilterSchema: z.ZodType<Prisma.NestedBoolWithAggregatesFilter>;
export declare const NestedDateTimeNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedDateTimeNullableWithAggregatesFilter>;
export declare const NestedJsonNullableFilterSchema: z.ZodType<Prisma.NestedJsonNullableFilter>;
export declare const NestedDateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedDateTimeWithAggregatesFilter>;
export declare const NestedIntNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedIntNullableWithAggregatesFilter>;
export declare const NestedFloatNullableFilterSchema: z.ZodType<Prisma.NestedFloatNullableFilter>;
export declare const NestedEnumReportTypeFilterSchema: z.ZodType<Prisma.NestedEnumReportTypeFilter>;
export declare const NestedEnumReportTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumReportTypeWithAggregatesFilter>;
export declare const NestedJsonFilterSchema: z.ZodType<Prisma.NestedJsonFilter>;
export declare const NestedEnumParkingTypeNullableFilterSchema: z.ZodType<Prisma.NestedEnumParkingTypeNullableFilter>;
export declare const NestedFloatNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedFloatNullableWithAggregatesFilter>;
export declare const NestedEnumParkingTypeNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumParkingTypeNullableWithAggregatesFilter>;
export declare const NestedIntWithAggregatesFilterSchema: z.ZodType<Prisma.NestedIntWithAggregatesFilter>;
export declare const NestedFloatFilterSchema: z.ZodType<Prisma.NestedFloatFilter>;
export declare const PageViewCreateWithoutUserInputSchema: z.ZodType<Prisma.PageViewCreateWithoutUserInput>;
export declare const PageViewUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.PageViewUncheckedCreateWithoutUserInput>;
export declare const PageViewCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.PageViewCreateOrConnectWithoutUserInput>;
export declare const PageViewCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.PageViewCreateManyUserInputEnvelope>;
export declare const ReportCreateWithoutUserInputSchema: z.ZodType<Prisma.ReportCreateWithoutUserInput>;
export declare const ReportUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.ReportUncheckedCreateWithoutUserInput>;
export declare const ReportCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.ReportCreateOrConnectWithoutUserInput>;
export declare const ReportCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.ReportCreateManyUserInputEnvelope>;
export declare const MallCreateWithoutUserInputSchema: z.ZodType<Prisma.MallCreateWithoutUserInput>;
export declare const MallUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutUserInput>;
export declare const MallCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutUserInput>;
export declare const MallCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.MallCreateManyUserInputEnvelope>;
export declare const ShopCreateWithoutUserInputSchema: z.ZodType<Prisma.ShopCreateWithoutUserInput>;
export declare const ShopUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.ShopUncheckedCreateWithoutUserInput>;
export declare const ShopCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.ShopCreateOrConnectWithoutUserInput>;
export declare const ShopCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.ShopCreateManyUserInputEnvelope>;
export declare const EventCreateWithoutUserInputSchema: z.ZodType<Prisma.EventCreateWithoutUserInput>;
export declare const EventUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.EventUncheckedCreateWithoutUserInput>;
export declare const EventCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.EventCreateOrConnectWithoutUserInput>;
export declare const EventCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.EventCreateManyUserInputEnvelope>;
export declare const OfferCreateWithoutUserInputSchema: z.ZodType<Prisma.OfferCreateWithoutUserInput>;
export declare const OfferUncheckedCreateWithoutUserInputSchema: z.ZodType<Prisma.OfferUncheckedCreateWithoutUserInput>;
export declare const OfferCreateOrConnectWithoutUserInputSchema: z.ZodType<Prisma.OfferCreateOrConnectWithoutUserInput>;
export declare const OfferCreateManyUserInputEnvelopeSchema: z.ZodType<Prisma.OfferCreateManyUserInputEnvelope>;
export declare const PageViewUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.PageViewUpsertWithWhereUniqueWithoutUserInput>;
export declare const PageViewUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.PageViewUpdateWithWhereUniqueWithoutUserInput>;
export declare const PageViewUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.PageViewUpdateManyWithWhereWithoutUserInput>;
export declare const PageViewScalarWhereInputSchema: z.ZodType<Prisma.PageViewScalarWhereInput>;
export declare const ReportUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.ReportUpsertWithWhereUniqueWithoutUserInput>;
export declare const ReportUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.ReportUpdateWithWhereUniqueWithoutUserInput>;
export declare const ReportUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.ReportUpdateManyWithWhereWithoutUserInput>;
export declare const ReportScalarWhereInputSchema: z.ZodType<Prisma.ReportScalarWhereInput>;
export declare const MallUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.MallUpsertWithWhereUniqueWithoutUserInput>;
export declare const MallUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.MallUpdateWithWhereUniqueWithoutUserInput>;
export declare const MallUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.MallUpdateManyWithWhereWithoutUserInput>;
export declare const MallScalarWhereInputSchema: z.ZodType<Prisma.MallScalarWhereInput>;
export declare const ShopUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.ShopUpsertWithWhereUniqueWithoutUserInput>;
export declare const ShopUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.ShopUpdateWithWhereUniqueWithoutUserInput>;
export declare const ShopUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.ShopUpdateManyWithWhereWithoutUserInput>;
export declare const ShopScalarWhereInputSchema: z.ZodType<Prisma.ShopScalarWhereInput>;
export declare const EventUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.EventUpsertWithWhereUniqueWithoutUserInput>;
export declare const EventUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.EventUpdateWithWhereUniqueWithoutUserInput>;
export declare const EventUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.EventUpdateManyWithWhereWithoutUserInput>;
export declare const EventScalarWhereInputSchema: z.ZodType<Prisma.EventScalarWhereInput>;
export declare const OfferUpsertWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.OfferUpsertWithWhereUniqueWithoutUserInput>;
export declare const OfferUpdateWithWhereUniqueWithoutUserInputSchema: z.ZodType<Prisma.OfferUpdateWithWhereUniqueWithoutUserInput>;
export declare const OfferUpdateManyWithWhereWithoutUserInputSchema: z.ZodType<Prisma.OfferUpdateManyWithWhereWithoutUserInput>;
export declare const OfferScalarWhereInputSchema: z.ZodType<Prisma.OfferScalarWhereInput>;
export declare const UserCreateWithoutPageViewsInputSchema: z.ZodType<Prisma.UserCreateWithoutPageViewsInput>;
export declare const UserUncheckedCreateWithoutPageViewsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutPageViewsInput>;
export declare const UserCreateOrConnectWithoutPageViewsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutPageViewsInput>;
export declare const UserUpsertWithoutPageViewsInputSchema: z.ZodType<Prisma.UserUpsertWithoutPageViewsInput>;
export declare const UserUpdateToOneWithWhereWithoutPageViewsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutPageViewsInput>;
export declare const UserUpdateWithoutPageViewsInputSchema: z.ZodType<Prisma.UserUpdateWithoutPageViewsInput>;
export declare const UserUncheckedUpdateWithoutPageViewsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutPageViewsInput>;
export declare const UserCreateWithoutReportsInputSchema: z.ZodType<Prisma.UserCreateWithoutReportsInput>;
export declare const UserUncheckedCreateWithoutReportsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutReportsInput>;
export declare const UserCreateOrConnectWithoutReportsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutReportsInput>;
export declare const UserUpsertWithoutReportsInputSchema: z.ZodType<Prisma.UserUpsertWithoutReportsInput>;
export declare const UserUpdateToOneWithWhereWithoutReportsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutReportsInput>;
export declare const UserUpdateWithoutReportsInputSchema: z.ZodType<Prisma.UserUpdateWithoutReportsInput>;
export declare const UserUncheckedUpdateWithoutReportsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutReportsInput>;
export declare const UserCreateWithoutMallsInputSchema: z.ZodType<Prisma.UserCreateWithoutMallsInput>;
export declare const UserUncheckedCreateWithoutMallsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutMallsInput>;
export declare const UserCreateOrConnectWithoutMallsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutMallsInput>;
export declare const MallHourCreateWithoutMallInputSchema: z.ZodType<Prisma.MallHourCreateWithoutMallInput>;
export declare const MallHourUncheckedCreateWithoutMallInputSchema: z.ZodType<Prisma.MallHourUncheckedCreateWithoutMallInput>;
export declare const MallHourCreateOrConnectWithoutMallInputSchema: z.ZodType<Prisma.MallHourCreateOrConnectWithoutMallInput>;
export declare const MallHourCreateManyMallInputEnvelopeSchema: z.ZodType<Prisma.MallHourCreateManyMallInputEnvelope>;
export declare const EventCreateWithoutMallInputSchema: z.ZodType<Prisma.EventCreateWithoutMallInput>;
export declare const EventUncheckedCreateWithoutMallInputSchema: z.ZodType<Prisma.EventUncheckedCreateWithoutMallInput>;
export declare const EventCreateOrConnectWithoutMallInputSchema: z.ZodType<Prisma.EventCreateOrConnectWithoutMallInput>;
export declare const EventCreateManyMallInputEnvelopeSchema: z.ZodType<Prisma.EventCreateManyMallInputEnvelope>;
export declare const FloorCreateWithoutMallInputSchema: z.ZodType<Prisma.FloorCreateWithoutMallInput>;
export declare const FloorUncheckedCreateWithoutMallInputSchema: z.ZodType<Prisma.FloorUncheckedCreateWithoutMallInput>;
export declare const FloorCreateOrConnectWithoutMallInputSchema: z.ZodType<Prisma.FloorCreateOrConnectWithoutMallInput>;
export declare const FloorCreateManyMallInputEnvelopeSchema: z.ZodType<Prisma.FloorCreateManyMallInputEnvelope>;
export declare const ShopCreateWithoutMallInputSchema: z.ZodType<Prisma.ShopCreateWithoutMallInput>;
export declare const ShopUncheckedCreateWithoutMallInputSchema: z.ZodType<Prisma.ShopUncheckedCreateWithoutMallInput>;
export declare const ShopCreateOrConnectWithoutMallInputSchema: z.ZodType<Prisma.ShopCreateOrConnectWithoutMallInput>;
export declare const ShopCreateManyMallInputEnvelopeSchema: z.ZodType<Prisma.ShopCreateManyMallInputEnvelope>;
export declare const OfferCreateWithoutMallInputSchema: z.ZodType<Prisma.OfferCreateWithoutMallInput>;
export declare const OfferUncheckedCreateWithoutMallInputSchema: z.ZodType<Prisma.OfferUncheckedCreateWithoutMallInput>;
export declare const OfferCreateOrConnectWithoutMallInputSchema: z.ZodType<Prisma.OfferCreateOrConnectWithoutMallInput>;
export declare const OfferCreateManyMallInputEnvelopeSchema: z.ZodType<Prisma.OfferCreateManyMallInputEnvelope>;
export declare const UserUpsertWithoutMallsInputSchema: z.ZodType<Prisma.UserUpsertWithoutMallsInput>;
export declare const UserUpdateToOneWithWhereWithoutMallsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutMallsInput>;
export declare const UserUpdateWithoutMallsInputSchema: z.ZodType<Prisma.UserUpdateWithoutMallsInput>;
export declare const UserUncheckedUpdateWithoutMallsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutMallsInput>;
export declare const MallHourUpsertWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.MallHourUpsertWithWhereUniqueWithoutMallInput>;
export declare const MallHourUpdateWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.MallHourUpdateWithWhereUniqueWithoutMallInput>;
export declare const MallHourUpdateManyWithWhereWithoutMallInputSchema: z.ZodType<Prisma.MallHourUpdateManyWithWhereWithoutMallInput>;
export declare const MallHourScalarWhereInputSchema: z.ZodType<Prisma.MallHourScalarWhereInput>;
export declare const EventUpsertWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.EventUpsertWithWhereUniqueWithoutMallInput>;
export declare const EventUpdateWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.EventUpdateWithWhereUniqueWithoutMallInput>;
export declare const EventUpdateManyWithWhereWithoutMallInputSchema: z.ZodType<Prisma.EventUpdateManyWithWhereWithoutMallInput>;
export declare const FloorUpsertWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.FloorUpsertWithWhereUniqueWithoutMallInput>;
export declare const FloorUpdateWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.FloorUpdateWithWhereUniqueWithoutMallInput>;
export declare const FloorUpdateManyWithWhereWithoutMallInputSchema: z.ZodType<Prisma.FloorUpdateManyWithWhereWithoutMallInput>;
export declare const FloorScalarWhereInputSchema: z.ZodType<Prisma.FloorScalarWhereInput>;
export declare const ShopUpsertWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.ShopUpsertWithWhereUniqueWithoutMallInput>;
export declare const ShopUpdateWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.ShopUpdateWithWhereUniqueWithoutMallInput>;
export declare const ShopUpdateManyWithWhereWithoutMallInputSchema: z.ZodType<Prisma.ShopUpdateManyWithWhereWithoutMallInput>;
export declare const OfferUpsertWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.OfferUpsertWithWhereUniqueWithoutMallInput>;
export declare const OfferUpdateWithWhereUniqueWithoutMallInputSchema: z.ZodType<Prisma.OfferUpdateWithWhereUniqueWithoutMallInput>;
export declare const OfferUpdateManyWithWhereWithoutMallInputSchema: z.ZodType<Prisma.OfferUpdateManyWithWhereWithoutMallInput>;
export declare const MallCreateWithoutMallHoursInputSchema: z.ZodType<Prisma.MallCreateWithoutMallHoursInput>;
export declare const MallUncheckedCreateWithoutMallHoursInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutMallHoursInput>;
export declare const MallCreateOrConnectWithoutMallHoursInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutMallHoursInput>;
export declare const MallUpsertWithoutMallHoursInputSchema: z.ZodType<Prisma.MallUpsertWithoutMallHoursInput>;
export declare const MallUpdateToOneWithWhereWithoutMallHoursInputSchema: z.ZodType<Prisma.MallUpdateToOneWithWhereWithoutMallHoursInput>;
export declare const MallUpdateWithoutMallHoursInputSchema: z.ZodType<Prisma.MallUpdateWithoutMallHoursInput>;
export declare const MallUncheckedUpdateWithoutMallHoursInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutMallHoursInput>;
export declare const MallCreateWithoutEventsInputSchema: z.ZodType<Prisma.MallCreateWithoutEventsInput>;
export declare const MallUncheckedCreateWithoutEventsInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutEventsInput>;
export declare const MallCreateOrConnectWithoutEventsInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutEventsInput>;
export declare const UserCreateWithoutEventsInputSchema: z.ZodType<Prisma.UserCreateWithoutEventsInput>;
export declare const UserUncheckedCreateWithoutEventsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutEventsInput>;
export declare const UserCreateOrConnectWithoutEventsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutEventsInput>;
export declare const MallUpsertWithoutEventsInputSchema: z.ZodType<Prisma.MallUpsertWithoutEventsInput>;
export declare const MallUpdateToOneWithWhereWithoutEventsInputSchema: z.ZodType<Prisma.MallUpdateToOneWithWhereWithoutEventsInput>;
export declare const MallUpdateWithoutEventsInputSchema: z.ZodType<Prisma.MallUpdateWithoutEventsInput>;
export declare const MallUncheckedUpdateWithoutEventsInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutEventsInput>;
export declare const UserUpsertWithoutEventsInputSchema: z.ZodType<Prisma.UserUpsertWithoutEventsInput>;
export declare const UserUpdateToOneWithWhereWithoutEventsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutEventsInput>;
export declare const UserUpdateWithoutEventsInputSchema: z.ZodType<Prisma.UserUpdateWithoutEventsInput>;
export declare const UserUncheckedUpdateWithoutEventsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutEventsInput>;
export declare const MallCreateWithoutOffersInputSchema: z.ZodType<Prisma.MallCreateWithoutOffersInput>;
export declare const MallUncheckedCreateWithoutOffersInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutOffersInput>;
export declare const MallCreateOrConnectWithoutOffersInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutOffersInput>;
export declare const UserCreateWithoutOffersInputSchema: z.ZodType<Prisma.UserCreateWithoutOffersInput>;
export declare const UserUncheckedCreateWithoutOffersInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutOffersInput>;
export declare const UserCreateOrConnectWithoutOffersInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutOffersInput>;
export declare const ShopOfferCreateWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferCreateWithoutOfferInput>;
export declare const ShopOfferUncheckedCreateWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUncheckedCreateWithoutOfferInput>;
export declare const ShopOfferCreateOrConnectWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferCreateOrConnectWithoutOfferInput>;
export declare const ShopOfferCreateManyOfferInputEnvelopeSchema: z.ZodType<Prisma.ShopOfferCreateManyOfferInputEnvelope>;
export declare const MallUpsertWithoutOffersInputSchema: z.ZodType<Prisma.MallUpsertWithoutOffersInput>;
export declare const MallUpdateToOneWithWhereWithoutOffersInputSchema: z.ZodType<Prisma.MallUpdateToOneWithWhereWithoutOffersInput>;
export declare const MallUpdateWithoutOffersInputSchema: z.ZodType<Prisma.MallUpdateWithoutOffersInput>;
export declare const MallUncheckedUpdateWithoutOffersInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutOffersInput>;
export declare const UserUpsertWithoutOffersInputSchema: z.ZodType<Prisma.UserUpsertWithoutOffersInput>;
export declare const UserUpdateToOneWithWhereWithoutOffersInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutOffersInput>;
export declare const UserUpdateWithoutOffersInputSchema: z.ZodType<Prisma.UserUpdateWithoutOffersInput>;
export declare const UserUncheckedUpdateWithoutOffersInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutOffersInput>;
export declare const ShopOfferUpsertWithWhereUniqueWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUpsertWithWhereUniqueWithoutOfferInput>;
export declare const ShopOfferUpdateWithWhereUniqueWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUpdateWithWhereUniqueWithoutOfferInput>;
export declare const ShopOfferUpdateManyWithWhereWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUpdateManyWithWhereWithoutOfferInput>;
export declare const ShopOfferScalarWhereInputSchema: z.ZodType<Prisma.ShopOfferScalarWhereInput>;
export declare const ShopCreateWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopCreateWithoutShopOffersInput>;
export declare const ShopUncheckedCreateWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopUncheckedCreateWithoutShopOffersInput>;
export declare const ShopCreateOrConnectWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopCreateOrConnectWithoutShopOffersInput>;
export declare const OfferCreateWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferCreateWithoutShopOffersInput>;
export declare const OfferUncheckedCreateWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferUncheckedCreateWithoutShopOffersInput>;
export declare const OfferCreateOrConnectWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferCreateOrConnectWithoutShopOffersInput>;
export declare const ShopUpsertWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopUpsertWithoutShopOffersInput>;
export declare const ShopUpdateToOneWithWhereWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopUpdateToOneWithWhereWithoutShopOffersInput>;
export declare const ShopUpdateWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopUpdateWithoutShopOffersInput>;
export declare const ShopUncheckedUpdateWithoutShopOffersInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateWithoutShopOffersInput>;
export declare const OfferUpsertWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferUpsertWithoutShopOffersInput>;
export declare const OfferUpdateToOneWithWhereWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferUpdateToOneWithWhereWithoutShopOffersInput>;
export declare const OfferUpdateWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferUpdateWithoutShopOffersInput>;
export declare const OfferUncheckedUpdateWithoutShopOffersInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateWithoutShopOffersInput>;
export declare const MallCreateWithoutFloorsInputSchema: z.ZodType<Prisma.MallCreateWithoutFloorsInput>;
export declare const MallUncheckedCreateWithoutFloorsInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutFloorsInput>;
export declare const MallCreateOrConnectWithoutFloorsInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutFloorsInput>;
export declare const ShopCreateWithoutFloorInputSchema: z.ZodType<Prisma.ShopCreateWithoutFloorInput>;
export declare const ShopUncheckedCreateWithoutFloorInputSchema: z.ZodType<Prisma.ShopUncheckedCreateWithoutFloorInput>;
export declare const ShopCreateOrConnectWithoutFloorInputSchema: z.ZodType<Prisma.ShopCreateOrConnectWithoutFloorInput>;
export declare const ShopCreateManyFloorInputEnvelopeSchema: z.ZodType<Prisma.ShopCreateManyFloorInputEnvelope>;
export declare const MallUpsertWithoutFloorsInputSchema: z.ZodType<Prisma.MallUpsertWithoutFloorsInput>;
export declare const MallUpdateToOneWithWhereWithoutFloorsInputSchema: z.ZodType<Prisma.MallUpdateToOneWithWhereWithoutFloorsInput>;
export declare const MallUpdateWithoutFloorsInputSchema: z.ZodType<Prisma.MallUpdateWithoutFloorsInput>;
export declare const MallUncheckedUpdateWithoutFloorsInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutFloorsInput>;
export declare const ShopUpsertWithWhereUniqueWithoutFloorInputSchema: z.ZodType<Prisma.ShopUpsertWithWhereUniqueWithoutFloorInput>;
export declare const ShopUpdateWithWhereUniqueWithoutFloorInputSchema: z.ZodType<Prisma.ShopUpdateWithWhereUniqueWithoutFloorInput>;
export declare const ShopUpdateManyWithWhereWithoutFloorInputSchema: z.ZodType<Prisma.ShopUpdateManyWithWhereWithoutFloorInput>;
export declare const MallCreateWithoutShopsInputSchema: z.ZodType<Prisma.MallCreateWithoutShopsInput>;
export declare const MallUncheckedCreateWithoutShopsInputSchema: z.ZodType<Prisma.MallUncheckedCreateWithoutShopsInput>;
export declare const MallCreateOrConnectWithoutShopsInputSchema: z.ZodType<Prisma.MallCreateOrConnectWithoutShopsInput>;
export declare const FloorCreateWithoutShopsInputSchema: z.ZodType<Prisma.FloorCreateWithoutShopsInput>;
export declare const FloorUncheckedCreateWithoutShopsInputSchema: z.ZodType<Prisma.FloorUncheckedCreateWithoutShopsInput>;
export declare const FloorCreateOrConnectWithoutShopsInputSchema: z.ZodType<Prisma.FloorCreateOrConnectWithoutShopsInput>;
export declare const UserCreateWithoutShopsInputSchema: z.ZodType<Prisma.UserCreateWithoutShopsInput>;
export declare const UserUncheckedCreateWithoutShopsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutShopsInput>;
export declare const UserCreateOrConnectWithoutShopsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutShopsInput>;
export declare const ShopHourCreateWithoutShopInputSchema: z.ZodType<Prisma.ShopHourCreateWithoutShopInput>;
export declare const ShopHourUncheckedCreateWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUncheckedCreateWithoutShopInput>;
export declare const ShopHourCreateOrConnectWithoutShopInputSchema: z.ZodType<Prisma.ShopHourCreateOrConnectWithoutShopInput>;
export declare const ShopHourCreateManyShopInputEnvelopeSchema: z.ZodType<Prisma.ShopHourCreateManyShopInputEnvelope>;
export declare const ShopOfferCreateWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferCreateWithoutShopInput>;
export declare const ShopOfferUncheckedCreateWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUncheckedCreateWithoutShopInput>;
export declare const ShopOfferCreateOrConnectWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferCreateOrConnectWithoutShopInput>;
export declare const ShopOfferCreateManyShopInputEnvelopeSchema: z.ZodType<Prisma.ShopOfferCreateManyShopInputEnvelope>;
export declare const MallUpsertWithoutShopsInputSchema: z.ZodType<Prisma.MallUpsertWithoutShopsInput>;
export declare const MallUpdateToOneWithWhereWithoutShopsInputSchema: z.ZodType<Prisma.MallUpdateToOneWithWhereWithoutShopsInput>;
export declare const MallUpdateWithoutShopsInputSchema: z.ZodType<Prisma.MallUpdateWithoutShopsInput>;
export declare const MallUncheckedUpdateWithoutShopsInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutShopsInput>;
export declare const FloorUpsertWithoutShopsInputSchema: z.ZodType<Prisma.FloorUpsertWithoutShopsInput>;
export declare const FloorUpdateToOneWithWhereWithoutShopsInputSchema: z.ZodType<Prisma.FloorUpdateToOneWithWhereWithoutShopsInput>;
export declare const FloorUpdateWithoutShopsInputSchema: z.ZodType<Prisma.FloorUpdateWithoutShopsInput>;
export declare const FloorUncheckedUpdateWithoutShopsInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateWithoutShopsInput>;
export declare const UserUpsertWithoutShopsInputSchema: z.ZodType<Prisma.UserUpsertWithoutShopsInput>;
export declare const UserUpdateToOneWithWhereWithoutShopsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutShopsInput>;
export declare const UserUpdateWithoutShopsInputSchema: z.ZodType<Prisma.UserUpdateWithoutShopsInput>;
export declare const UserUncheckedUpdateWithoutShopsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutShopsInput>;
export declare const ShopHourUpsertWithWhereUniqueWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUpsertWithWhereUniqueWithoutShopInput>;
export declare const ShopHourUpdateWithWhereUniqueWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUpdateWithWhereUniqueWithoutShopInput>;
export declare const ShopHourUpdateManyWithWhereWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUpdateManyWithWhereWithoutShopInput>;
export declare const ShopHourScalarWhereInputSchema: z.ZodType<Prisma.ShopHourScalarWhereInput>;
export declare const ShopOfferUpsertWithWhereUniqueWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUpsertWithWhereUniqueWithoutShopInput>;
export declare const ShopOfferUpdateWithWhereUniqueWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUpdateWithWhereUniqueWithoutShopInput>;
export declare const ShopOfferUpdateManyWithWhereWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUpdateManyWithWhereWithoutShopInput>;
export declare const ShopCreateWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopCreateWithoutShopHoursInput>;
export declare const ShopUncheckedCreateWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopUncheckedCreateWithoutShopHoursInput>;
export declare const ShopCreateOrConnectWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopCreateOrConnectWithoutShopHoursInput>;
export declare const ShopUpsertWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopUpsertWithoutShopHoursInput>;
export declare const ShopUpdateToOneWithWhereWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopUpdateToOneWithWhereWithoutShopHoursInput>;
export declare const ShopUpdateWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopUpdateWithoutShopHoursInput>;
export declare const ShopUncheckedUpdateWithoutShopHoursInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateWithoutShopHoursInput>;
export declare const PageViewCreateManyUserInputSchema: z.ZodType<Prisma.PageViewCreateManyUserInput>;
export declare const ReportCreateManyUserInputSchema: z.ZodType<Prisma.ReportCreateManyUserInput>;
export declare const MallCreateManyUserInputSchema: z.ZodType<Prisma.MallCreateManyUserInput>;
export declare const ShopCreateManyUserInputSchema: z.ZodType<Prisma.ShopCreateManyUserInput>;
export declare const EventCreateManyUserInputSchema: z.ZodType<Prisma.EventCreateManyUserInput>;
export declare const OfferCreateManyUserInputSchema: z.ZodType<Prisma.OfferCreateManyUserInput>;
export declare const PageViewUpdateWithoutUserInputSchema: z.ZodType<Prisma.PageViewUpdateWithoutUserInput>;
export declare const PageViewUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.PageViewUncheckedUpdateWithoutUserInput>;
export declare const PageViewUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.PageViewUncheckedUpdateManyWithoutUserInput>;
export declare const ReportUpdateWithoutUserInputSchema: z.ZodType<Prisma.ReportUpdateWithoutUserInput>;
export declare const ReportUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.ReportUncheckedUpdateWithoutUserInput>;
export declare const ReportUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.ReportUncheckedUpdateManyWithoutUserInput>;
export declare const MallUpdateWithoutUserInputSchema: z.ZodType<Prisma.MallUpdateWithoutUserInput>;
export declare const MallUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.MallUncheckedUpdateWithoutUserInput>;
export declare const MallUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.MallUncheckedUpdateManyWithoutUserInput>;
export declare const ShopUpdateWithoutUserInputSchema: z.ZodType<Prisma.ShopUpdateWithoutUserInput>;
export declare const ShopUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateWithoutUserInput>;
export declare const ShopUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutUserInput>;
export declare const EventUpdateWithoutUserInputSchema: z.ZodType<Prisma.EventUpdateWithoutUserInput>;
export declare const EventUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.EventUncheckedUpdateWithoutUserInput>;
export declare const EventUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.EventUncheckedUpdateManyWithoutUserInput>;
export declare const OfferUpdateWithoutUserInputSchema: z.ZodType<Prisma.OfferUpdateWithoutUserInput>;
export declare const OfferUncheckedUpdateWithoutUserInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateWithoutUserInput>;
export declare const OfferUncheckedUpdateManyWithoutUserInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateManyWithoutUserInput>;
export declare const MallHourCreateManyMallInputSchema: z.ZodType<Prisma.MallHourCreateManyMallInput>;
export declare const EventCreateManyMallInputSchema: z.ZodType<Prisma.EventCreateManyMallInput>;
export declare const FloorCreateManyMallInputSchema: z.ZodType<Prisma.FloorCreateManyMallInput>;
export declare const ShopCreateManyMallInputSchema: z.ZodType<Prisma.ShopCreateManyMallInput>;
export declare const OfferCreateManyMallInputSchema: z.ZodType<Prisma.OfferCreateManyMallInput>;
export declare const MallHourUpdateWithoutMallInputSchema: z.ZodType<Prisma.MallHourUpdateWithoutMallInput>;
export declare const MallHourUncheckedUpdateWithoutMallInputSchema: z.ZodType<Prisma.MallHourUncheckedUpdateWithoutMallInput>;
export declare const MallHourUncheckedUpdateManyWithoutMallInputSchema: z.ZodType<Prisma.MallHourUncheckedUpdateManyWithoutMallInput>;
export declare const EventUpdateWithoutMallInputSchema: z.ZodType<Prisma.EventUpdateWithoutMallInput>;
export declare const EventUncheckedUpdateWithoutMallInputSchema: z.ZodType<Prisma.EventUncheckedUpdateWithoutMallInput>;
export declare const EventUncheckedUpdateManyWithoutMallInputSchema: z.ZodType<Prisma.EventUncheckedUpdateManyWithoutMallInput>;
export declare const FloorUpdateWithoutMallInputSchema: z.ZodType<Prisma.FloorUpdateWithoutMallInput>;
export declare const FloorUncheckedUpdateWithoutMallInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateWithoutMallInput>;
export declare const FloorUncheckedUpdateManyWithoutMallInputSchema: z.ZodType<Prisma.FloorUncheckedUpdateManyWithoutMallInput>;
export declare const ShopUpdateWithoutMallInputSchema: z.ZodType<Prisma.ShopUpdateWithoutMallInput>;
export declare const ShopUncheckedUpdateWithoutMallInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateWithoutMallInput>;
export declare const ShopUncheckedUpdateManyWithoutMallInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutMallInput>;
export declare const OfferUpdateWithoutMallInputSchema: z.ZodType<Prisma.OfferUpdateWithoutMallInput>;
export declare const OfferUncheckedUpdateWithoutMallInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateWithoutMallInput>;
export declare const OfferUncheckedUpdateManyWithoutMallInputSchema: z.ZodType<Prisma.OfferUncheckedUpdateManyWithoutMallInput>;
export declare const ShopOfferCreateManyOfferInputSchema: z.ZodType<Prisma.ShopOfferCreateManyOfferInput>;
export declare const ShopOfferUpdateWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUpdateWithoutOfferInput>;
export declare const ShopOfferUncheckedUpdateWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateWithoutOfferInput>;
export declare const ShopOfferUncheckedUpdateManyWithoutOfferInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateManyWithoutOfferInput>;
export declare const ShopCreateManyFloorInputSchema: z.ZodType<Prisma.ShopCreateManyFloorInput>;
export declare const ShopUpdateWithoutFloorInputSchema: z.ZodType<Prisma.ShopUpdateWithoutFloorInput>;
export declare const ShopUncheckedUpdateWithoutFloorInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateWithoutFloorInput>;
export declare const ShopUncheckedUpdateManyWithoutFloorInputSchema: z.ZodType<Prisma.ShopUncheckedUpdateManyWithoutFloorInput>;
export declare const ShopHourCreateManyShopInputSchema: z.ZodType<Prisma.ShopHourCreateManyShopInput>;
export declare const ShopOfferCreateManyShopInputSchema: z.ZodType<Prisma.ShopOfferCreateManyShopInput>;
export declare const ShopHourUpdateWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUpdateWithoutShopInput>;
export declare const ShopHourUncheckedUpdateWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUncheckedUpdateWithoutShopInput>;
export declare const ShopHourUncheckedUpdateManyWithoutShopInputSchema: z.ZodType<Prisma.ShopHourUncheckedUpdateManyWithoutShopInput>;
export declare const ShopOfferUpdateWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUpdateWithoutShopInput>;
export declare const ShopOfferUncheckedUpdateWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateWithoutShopInput>;
export declare const ShopOfferUncheckedUpdateManyWithoutShopInputSchema: z.ZodType<Prisma.ShopOfferUncheckedUpdateManyWithoutShopInput>;
export declare const UserFindFirstArgsSchema: z.ZodType<Prisma.UserFindFirstArgs>;
export declare const UserFindFirstOrThrowArgsSchema: z.ZodType<Prisma.UserFindFirstOrThrowArgs>;
export declare const UserFindManyArgsSchema: z.ZodType<Prisma.UserFindManyArgs>;
export declare const UserAggregateArgsSchema: z.ZodType<Prisma.UserAggregateArgs>;
export declare const UserGroupByArgsSchema: z.ZodType<Prisma.UserGroupByArgs>;
export declare const UserFindUniqueArgsSchema: z.ZodType<Prisma.UserFindUniqueArgs>;
export declare const UserFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.UserFindUniqueOrThrowArgs>;
export declare const PageViewFindFirstArgsSchema: z.ZodType<Prisma.PageViewFindFirstArgs>;
export declare const PageViewFindFirstOrThrowArgsSchema: z.ZodType<Prisma.PageViewFindFirstOrThrowArgs>;
export declare const PageViewFindManyArgsSchema: z.ZodType<Prisma.PageViewFindManyArgs>;
export declare const PageViewAggregateArgsSchema: z.ZodType<Prisma.PageViewAggregateArgs>;
export declare const PageViewGroupByArgsSchema: z.ZodType<Prisma.PageViewGroupByArgs>;
export declare const PageViewFindUniqueArgsSchema: z.ZodType<Prisma.PageViewFindUniqueArgs>;
export declare const PageViewFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.PageViewFindUniqueOrThrowArgs>;
export declare const ReportFindFirstArgsSchema: z.ZodType<Prisma.ReportFindFirstArgs>;
export declare const ReportFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ReportFindFirstOrThrowArgs>;
export declare const ReportFindManyArgsSchema: z.ZodType<Prisma.ReportFindManyArgs>;
export declare const ReportAggregateArgsSchema: z.ZodType<Prisma.ReportAggregateArgs>;
export declare const ReportGroupByArgsSchema: z.ZodType<Prisma.ReportGroupByArgs>;
export declare const ReportFindUniqueArgsSchema: z.ZodType<Prisma.ReportFindUniqueArgs>;
export declare const ReportFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ReportFindUniqueOrThrowArgs>;
export declare const MallFindFirstArgsSchema: z.ZodType<Prisma.MallFindFirstArgs>;
export declare const MallFindFirstOrThrowArgsSchema: z.ZodType<Prisma.MallFindFirstOrThrowArgs>;
export declare const MallFindManyArgsSchema: z.ZodType<Prisma.MallFindManyArgs>;
export declare const MallAggregateArgsSchema: z.ZodType<Prisma.MallAggregateArgs>;
export declare const MallGroupByArgsSchema: z.ZodType<Prisma.MallGroupByArgs>;
export declare const MallFindUniqueArgsSchema: z.ZodType<Prisma.MallFindUniqueArgs>;
export declare const MallFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.MallFindUniqueOrThrowArgs>;
export declare const MallHourFindFirstArgsSchema: z.ZodType<Prisma.MallHourFindFirstArgs>;
export declare const MallHourFindFirstOrThrowArgsSchema: z.ZodType<Prisma.MallHourFindFirstOrThrowArgs>;
export declare const MallHourFindManyArgsSchema: z.ZodType<Prisma.MallHourFindManyArgs>;
export declare const MallHourAggregateArgsSchema: z.ZodType<Prisma.MallHourAggregateArgs>;
export declare const MallHourGroupByArgsSchema: z.ZodType<Prisma.MallHourGroupByArgs>;
export declare const MallHourFindUniqueArgsSchema: z.ZodType<Prisma.MallHourFindUniqueArgs>;
export declare const MallHourFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.MallHourFindUniqueOrThrowArgs>;
export declare const EventFindFirstArgsSchema: z.ZodType<Prisma.EventFindFirstArgs>;
export declare const EventFindFirstOrThrowArgsSchema: z.ZodType<Prisma.EventFindFirstOrThrowArgs>;
export declare const EventFindManyArgsSchema: z.ZodType<Prisma.EventFindManyArgs>;
export declare const EventAggregateArgsSchema: z.ZodType<Prisma.EventAggregateArgs>;
export declare const EventGroupByArgsSchema: z.ZodType<Prisma.EventGroupByArgs>;
export declare const EventFindUniqueArgsSchema: z.ZodType<Prisma.EventFindUniqueArgs>;
export declare const EventFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.EventFindUniqueOrThrowArgs>;
export declare const OfferFindFirstArgsSchema: z.ZodType<Prisma.OfferFindFirstArgs>;
export declare const OfferFindFirstOrThrowArgsSchema: z.ZodType<Prisma.OfferFindFirstOrThrowArgs>;
export declare const OfferFindManyArgsSchema: z.ZodType<Prisma.OfferFindManyArgs>;
export declare const OfferAggregateArgsSchema: z.ZodType<Prisma.OfferAggregateArgs>;
export declare const OfferGroupByArgsSchema: z.ZodType<Prisma.OfferGroupByArgs>;
export declare const OfferFindUniqueArgsSchema: z.ZodType<Prisma.OfferFindUniqueArgs>;
export declare const OfferFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.OfferFindUniqueOrThrowArgs>;
export declare const ShopOfferFindFirstArgsSchema: z.ZodType<Prisma.ShopOfferFindFirstArgs>;
export declare const ShopOfferFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ShopOfferFindFirstOrThrowArgs>;
export declare const ShopOfferFindManyArgsSchema: z.ZodType<Prisma.ShopOfferFindManyArgs>;
export declare const ShopOfferAggregateArgsSchema: z.ZodType<Prisma.ShopOfferAggregateArgs>;
export declare const ShopOfferGroupByArgsSchema: z.ZodType<Prisma.ShopOfferGroupByArgs>;
export declare const ShopOfferFindUniqueArgsSchema: z.ZodType<Prisma.ShopOfferFindUniqueArgs>;
export declare const ShopOfferFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ShopOfferFindUniqueOrThrowArgs>;
export declare const FloorFindFirstArgsSchema: z.ZodType<Prisma.FloorFindFirstArgs>;
export declare const FloorFindFirstOrThrowArgsSchema: z.ZodType<Prisma.FloorFindFirstOrThrowArgs>;
export declare const FloorFindManyArgsSchema: z.ZodType<Prisma.FloorFindManyArgs>;
export declare const FloorAggregateArgsSchema: z.ZodType<Prisma.FloorAggregateArgs>;
export declare const FloorGroupByArgsSchema: z.ZodType<Prisma.FloorGroupByArgs>;
export declare const FloorFindUniqueArgsSchema: z.ZodType<Prisma.FloorFindUniqueArgs>;
export declare const FloorFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.FloorFindUniqueOrThrowArgs>;
export declare const ShopFindFirstArgsSchema: z.ZodType<Prisma.ShopFindFirstArgs>;
export declare const ShopFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ShopFindFirstOrThrowArgs>;
export declare const ShopFindManyArgsSchema: z.ZodType<Prisma.ShopFindManyArgs>;
export declare const ShopAggregateArgsSchema: z.ZodType<Prisma.ShopAggregateArgs>;
export declare const ShopGroupByArgsSchema: z.ZodType<Prisma.ShopGroupByArgs>;
export declare const ShopFindUniqueArgsSchema: z.ZodType<Prisma.ShopFindUniqueArgs>;
export declare const ShopFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ShopFindUniqueOrThrowArgs>;
export declare const ShopHourFindFirstArgsSchema: z.ZodType<Prisma.ShopHourFindFirstArgs>;
export declare const ShopHourFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ShopHourFindFirstOrThrowArgs>;
export declare const ShopHourFindManyArgsSchema: z.ZodType<Prisma.ShopHourFindManyArgs>;
export declare const ShopHourAggregateArgsSchema: z.ZodType<Prisma.ShopHourAggregateArgs>;
export declare const ShopHourGroupByArgsSchema: z.ZodType<Prisma.ShopHourGroupByArgs>;
export declare const ShopHourFindUniqueArgsSchema: z.ZodType<Prisma.ShopHourFindUniqueArgs>;
export declare const ShopHourFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ShopHourFindUniqueOrThrowArgs>;
export declare const UserCreateArgsSchema: z.ZodType<Prisma.UserCreateArgs>;
export declare const UserUpsertArgsSchema: z.ZodType<Prisma.UserUpsertArgs>;
export declare const UserCreateManyArgsSchema: z.ZodType<Prisma.UserCreateManyArgs>;
export declare const UserCreateManyAndReturnArgsSchema: z.ZodType<Prisma.UserCreateManyAndReturnArgs>;
export declare const UserDeleteArgsSchema: z.ZodType<Prisma.UserDeleteArgs>;
export declare const UserUpdateArgsSchema: z.ZodType<Prisma.UserUpdateArgs>;
export declare const UserUpdateManyArgsSchema: z.ZodType<Prisma.UserUpdateManyArgs>;
export declare const UserUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.UserUpdateManyAndReturnArgs>;
export declare const UserDeleteManyArgsSchema: z.ZodType<Prisma.UserDeleteManyArgs>;
export declare const PageViewCreateArgsSchema: z.ZodType<Prisma.PageViewCreateArgs>;
export declare const PageViewUpsertArgsSchema: z.ZodType<Prisma.PageViewUpsertArgs>;
export declare const PageViewCreateManyArgsSchema: z.ZodType<Prisma.PageViewCreateManyArgs>;
export declare const PageViewCreateManyAndReturnArgsSchema: z.ZodType<Prisma.PageViewCreateManyAndReturnArgs>;
export declare const PageViewDeleteArgsSchema: z.ZodType<Prisma.PageViewDeleteArgs>;
export declare const PageViewUpdateArgsSchema: z.ZodType<Prisma.PageViewUpdateArgs>;
export declare const PageViewUpdateManyArgsSchema: z.ZodType<Prisma.PageViewUpdateManyArgs>;
export declare const PageViewUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.PageViewUpdateManyAndReturnArgs>;
export declare const PageViewDeleteManyArgsSchema: z.ZodType<Prisma.PageViewDeleteManyArgs>;
export declare const ReportCreateArgsSchema: z.ZodType<Prisma.ReportCreateArgs>;
export declare const ReportUpsertArgsSchema: z.ZodType<Prisma.ReportUpsertArgs>;
export declare const ReportCreateManyArgsSchema: z.ZodType<Prisma.ReportCreateManyArgs>;
export declare const ReportCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ReportCreateManyAndReturnArgs>;
export declare const ReportDeleteArgsSchema: z.ZodType<Prisma.ReportDeleteArgs>;
export declare const ReportUpdateArgsSchema: z.ZodType<Prisma.ReportUpdateArgs>;
export declare const ReportUpdateManyArgsSchema: z.ZodType<Prisma.ReportUpdateManyArgs>;
export declare const ReportUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ReportUpdateManyAndReturnArgs>;
export declare const ReportDeleteManyArgsSchema: z.ZodType<Prisma.ReportDeleteManyArgs>;
export declare const MallCreateArgsSchema: z.ZodType<Prisma.MallCreateArgs>;
export declare const MallUpsertArgsSchema: z.ZodType<Prisma.MallUpsertArgs>;
export declare const MallCreateManyArgsSchema: z.ZodType<Prisma.MallCreateManyArgs>;
export declare const MallCreateManyAndReturnArgsSchema: z.ZodType<Prisma.MallCreateManyAndReturnArgs>;
export declare const MallDeleteArgsSchema: z.ZodType<Prisma.MallDeleteArgs>;
export declare const MallUpdateArgsSchema: z.ZodType<Prisma.MallUpdateArgs>;
export declare const MallUpdateManyArgsSchema: z.ZodType<Prisma.MallUpdateManyArgs>;
export declare const MallUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.MallUpdateManyAndReturnArgs>;
export declare const MallDeleteManyArgsSchema: z.ZodType<Prisma.MallDeleteManyArgs>;
export declare const MallHourCreateArgsSchema: z.ZodType<Prisma.MallHourCreateArgs>;
export declare const MallHourUpsertArgsSchema: z.ZodType<Prisma.MallHourUpsertArgs>;
export declare const MallHourCreateManyArgsSchema: z.ZodType<Prisma.MallHourCreateManyArgs>;
export declare const MallHourCreateManyAndReturnArgsSchema: z.ZodType<Prisma.MallHourCreateManyAndReturnArgs>;
export declare const MallHourDeleteArgsSchema: z.ZodType<Prisma.MallHourDeleteArgs>;
export declare const MallHourUpdateArgsSchema: z.ZodType<Prisma.MallHourUpdateArgs>;
export declare const MallHourUpdateManyArgsSchema: z.ZodType<Prisma.MallHourUpdateManyArgs>;
export declare const MallHourUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.MallHourUpdateManyAndReturnArgs>;
export declare const MallHourDeleteManyArgsSchema: z.ZodType<Prisma.MallHourDeleteManyArgs>;
export declare const EventCreateArgsSchema: z.ZodType<Prisma.EventCreateArgs>;
export declare const EventUpsertArgsSchema: z.ZodType<Prisma.EventUpsertArgs>;
export declare const EventCreateManyArgsSchema: z.ZodType<Prisma.EventCreateManyArgs>;
export declare const EventCreateManyAndReturnArgsSchema: z.ZodType<Prisma.EventCreateManyAndReturnArgs>;
export declare const EventDeleteArgsSchema: z.ZodType<Prisma.EventDeleteArgs>;
export declare const EventUpdateArgsSchema: z.ZodType<Prisma.EventUpdateArgs>;
export declare const EventUpdateManyArgsSchema: z.ZodType<Prisma.EventUpdateManyArgs>;
export declare const EventUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.EventUpdateManyAndReturnArgs>;
export declare const EventDeleteManyArgsSchema: z.ZodType<Prisma.EventDeleteManyArgs>;
export declare const OfferCreateArgsSchema: z.ZodType<Prisma.OfferCreateArgs>;
export declare const OfferUpsertArgsSchema: z.ZodType<Prisma.OfferUpsertArgs>;
export declare const OfferCreateManyArgsSchema: z.ZodType<Prisma.OfferCreateManyArgs>;
export declare const OfferCreateManyAndReturnArgsSchema: z.ZodType<Prisma.OfferCreateManyAndReturnArgs>;
export declare const OfferDeleteArgsSchema: z.ZodType<Prisma.OfferDeleteArgs>;
export declare const OfferUpdateArgsSchema: z.ZodType<Prisma.OfferUpdateArgs>;
export declare const OfferUpdateManyArgsSchema: z.ZodType<Prisma.OfferUpdateManyArgs>;
export declare const OfferUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.OfferUpdateManyAndReturnArgs>;
export declare const OfferDeleteManyArgsSchema: z.ZodType<Prisma.OfferDeleteManyArgs>;
export declare const ShopOfferCreateArgsSchema: z.ZodType<Prisma.ShopOfferCreateArgs>;
export declare const ShopOfferUpsertArgsSchema: z.ZodType<Prisma.ShopOfferUpsertArgs>;
export declare const ShopOfferCreateManyArgsSchema: z.ZodType<Prisma.ShopOfferCreateManyArgs>;
export declare const ShopOfferCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopOfferCreateManyAndReturnArgs>;
export declare const ShopOfferDeleteArgsSchema: z.ZodType<Prisma.ShopOfferDeleteArgs>;
export declare const ShopOfferUpdateArgsSchema: z.ZodType<Prisma.ShopOfferUpdateArgs>;
export declare const ShopOfferUpdateManyArgsSchema: z.ZodType<Prisma.ShopOfferUpdateManyArgs>;
export declare const ShopOfferUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopOfferUpdateManyAndReturnArgs>;
export declare const ShopOfferDeleteManyArgsSchema: z.ZodType<Prisma.ShopOfferDeleteManyArgs>;
export declare const FloorCreateArgsSchema: z.ZodType<Prisma.FloorCreateArgs>;
export declare const FloorUpsertArgsSchema: z.ZodType<Prisma.FloorUpsertArgs>;
export declare const FloorCreateManyArgsSchema: z.ZodType<Prisma.FloorCreateManyArgs>;
export declare const FloorCreateManyAndReturnArgsSchema: z.ZodType<Prisma.FloorCreateManyAndReturnArgs>;
export declare const FloorDeleteArgsSchema: z.ZodType<Prisma.FloorDeleteArgs>;
export declare const FloorUpdateArgsSchema: z.ZodType<Prisma.FloorUpdateArgs>;
export declare const FloorUpdateManyArgsSchema: z.ZodType<Prisma.FloorUpdateManyArgs>;
export declare const FloorUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.FloorUpdateManyAndReturnArgs>;
export declare const FloorDeleteManyArgsSchema: z.ZodType<Prisma.FloorDeleteManyArgs>;
export declare const ShopCreateArgsSchema: z.ZodType<Prisma.ShopCreateArgs>;
export declare const ShopUpsertArgsSchema: z.ZodType<Prisma.ShopUpsertArgs>;
export declare const ShopCreateManyArgsSchema: z.ZodType<Prisma.ShopCreateManyArgs>;
export declare const ShopCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopCreateManyAndReturnArgs>;
export declare const ShopDeleteArgsSchema: z.ZodType<Prisma.ShopDeleteArgs>;
export declare const ShopUpdateArgsSchema: z.ZodType<Prisma.ShopUpdateArgs>;
export declare const ShopUpdateManyArgsSchema: z.ZodType<Prisma.ShopUpdateManyArgs>;
export declare const ShopUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopUpdateManyAndReturnArgs>;
export declare const ShopDeleteManyArgsSchema: z.ZodType<Prisma.ShopDeleteManyArgs>;
export declare const ShopHourCreateArgsSchema: z.ZodType<Prisma.ShopHourCreateArgs>;
export declare const ShopHourUpsertArgsSchema: z.ZodType<Prisma.ShopHourUpsertArgs>;
export declare const ShopHourCreateManyArgsSchema: z.ZodType<Prisma.ShopHourCreateManyArgs>;
export declare const ShopHourCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopHourCreateManyAndReturnArgs>;
export declare const ShopHourDeleteArgsSchema: z.ZodType<Prisma.ShopHourDeleteArgs>;
export declare const ShopHourUpdateArgsSchema: z.ZodType<Prisma.ShopHourUpdateArgs>;
export declare const ShopHourUpdateManyArgsSchema: z.ZodType<Prisma.ShopHourUpdateManyArgs>;
export declare const ShopHourUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ShopHourUpdateManyAndReturnArgs>;
export declare const ShopHourDeleteManyArgsSchema: z.ZodType<Prisma.ShopHourDeleteManyArgs>;
