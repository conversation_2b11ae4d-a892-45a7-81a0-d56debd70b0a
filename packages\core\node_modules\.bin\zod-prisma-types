#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules/zod-prisma-types/dist/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules/zod-prisma-types/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules/zod-prisma-types/dist/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules/zod-prisma-types/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/zod-prisma-types@3.2.4_@pri_86ceae1db79716fccfcfaf7fb6101e3d/node_modules:/mnt/c/Coding/Websites/mallsurf/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../zod-prisma-types/dist/bin.js" "$@"
else
  exec node  "$basedir/../zod-prisma-types/dist/bin.js" "$@"
fi
