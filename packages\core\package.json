{"name": "@mallsurf/core", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@prisma/client": "^6.10.1", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "next": "15.3.4"}, "devDependencies": {"@types/node": "^20", "typescript": "^5"}}