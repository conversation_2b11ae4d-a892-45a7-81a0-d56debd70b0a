export interface ApiResponse<T> {
    success: boolean;
    message?: string;
    data?: T;
    error?: string;
    errors?: string[];
    status?: number;
    code?: string;
    stack?: string;
    meta?: {
        page?: number;
        limit?: number;
        total?: number;
        hasNext?: boolean;
    };
}

export type ApiHandler<T> = (
    req: Request,
    res: Response
) => Promise<Response | ApiResponse<T>>;
