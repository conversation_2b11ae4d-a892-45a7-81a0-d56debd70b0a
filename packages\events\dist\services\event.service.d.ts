import { Event, EventCreateInputSchema, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const createEvent: (input: z.infer<typeof EventCreateInputSchema>, mallId: string, userId: string) => Promise<ApiResponse<Event>>;
export declare const updateEvent: (input: Partial<z.infer<typeof EventCreateInputSchema>>, eventId: string, userId: string) => Promise<ApiResponse<Event>>;
export declare const deleteEvent: (eventId: string) => Promise<ApiResponse<{
    message: string;
}>>;
export declare const getEvents: (mallId: string, page: number, limit: number, dateFrom?: Date, dateTo?: Date) => Promise<ApiResponse<Event[]>>;
export declare const getEvent: (eventId: string) => Promise<ApiResponse<Event | null>>;
