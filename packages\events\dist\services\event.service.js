"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEvent = exports.getEvents = exports.deleteEvent = exports.updateEvent = exports.createEvent = void 0;
const core_1 = require("@mallsurf/core");
const createEvent = async (input, mallId, userId) => {
    const event = await core_1.prisma.event.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });
    return {
        success: true,
        data: event,
    };
};
exports.createEvent = createEvent;
const updateEvent = async (input, eventId, userId) => {
    const event = await core_1.prisma.event.update({
        where: { id: eventId },
        data: {
            ...input,
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });
    return {
        success: true,
        data: event,
    };
};
exports.updateEvent = updateEvent;
const deleteEvent = async (eventId) => {
    await core_1.prisma.event.delete({
        where: { id: eventId },
    });
    return {
        success: true,
        data: { message: "Event deleted" },
    };
};
exports.deleteEvent = deleteEvent;
const getEvents = async (mallId, page, limit, dateFrom, dateTo) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const events = await core_1.prisma.event.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(dateFrom && { date: { gte: dateFrom } }),
            ...(dateTo && { date: { lte: dateTo } }),
        }
    });
    const total = await core_1.prisma.event.count({
        where: {
            mallId,
            ...(dateFrom && { date: { gte: dateFrom } }),
            ...(dateTo && { date: { lte: dateTo } }),
        }
    });
    return {
        success: true,
        data: events,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getEvents = getEvents;
const getEvent = async (eventId) => {
    const event = await core_1.prisma.event.findUnique({
        where: { id: eventId },
    });
    return {
        success: true,
        data: event,
    };
};
exports.getEvent = getEvent;
