import { Event, EventCreateInputSchema, ApiResponse, prisma } from "@mallsurf/core"
import { z } from "zod";

export const createEvent = async (
    input: z.infer<typeof EventCreateInputSchema>,
    mallId: string,
    userId: string
): Promise<ApiResponse<Event>> => {
    const event = await prisma.event.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });

    return {
        success: true,
        data: event,
    };
}

export const updateEvent = async (
    input: Partial<z.infer<typeof EventCreateInputSchema>>,
    eventId: string,
    userId: string
): Promise<ApiResponse<Event>> => {
    const event = await prisma.event.update({
        where: { id: eventId },
        data: {
            ...input,
            user: {
                connect: {
                    id: userId,
                }
            },
        },

    });

    return {
        success: true,
        data: event,
    };
}

export const deleteEvent = async (eventId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.event.delete({
        where: { id: eventId },
    });

    return {
        success: true,
        data: { message: "Event deleted" },
    };
}

export const getEvents = async (
    mallId: string,
    page: number,
    limit: number,
    dateFrom?: Date,
    dateTo?: Date
): Promise<ApiResponse<Event[]>> => {
    const skip = (page - 1) * limit;
    const take = limit;

    const events = await prisma.event.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(dateFrom && { date: { gte: dateFrom } }),
            ...(dateTo && { date: { lte: dateTo } }),
        }
    });

    const total = await prisma.event.count({
        where: {
            mallId,
            ...(dateFrom && { date: { gte: dateFrom } }),
            ...(dateTo && { date: { lte: dateTo } }),
        }
    });

    return {
        success: true,
        data: events,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
}

export const getEvent = async (eventId: string): Promise<ApiResponse<Event | null>> => {
    const event = await prisma.event.findUnique({
        where: { id: eventId },
    });

    return {
        success: true,
        data: event,
    };
}

