name: Update Dependencies

on:
  schedule:
    # Run weekly on Mondays at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    
    permissions:
      contents: write
      pull-requests: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        registry-url: 'https://npm.pkg.github.com'
        scope: '@mallsurf'
    
    - name: Update @mallsurf dependencies
      run: |
        # Update @mallsurf packages to latest versions
        npm update @mallsurf/core @mallsurf/auth --save
      env:
        NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Install updated dependencies
      run: npm install
      env:
        NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Run tests
      run: npm test --if-present
    
    - name: Build package
      run: npm run build
    
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update @mallsurf dependencies'
        title: 'Update @mallsurf dependencies'
        body: |
          This PR updates @mallsurf package dependencies to their latest versions.
          
          - Updated @mallsurf/core
          - Updated @mallsurf/auth (if applicable)
          
          Please review the changes and ensure all tests pass before merging.
        branch: update-mallsurf-dependencies
        delete-branch: true
