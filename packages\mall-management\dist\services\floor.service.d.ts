import { Floor, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
declare const FloorCreateInputSchema: z.ZodObject<{
    name: z.ZodString;
    level: z.ZodNumber;
    description: z.ZodOptional<z.ZodString>;
    mapImage: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name: string;
    isActive: boolean;
    level: number;
    description?: string | undefined;
    mapImage?: string | undefined;
}, {
    name: string;
    level: number;
    description?: string | undefined;
    isActive?: boolean | undefined;
    mapImage?: string | undefined;
}>;
export declare const createFloor: (input: z.infer<typeof FloorCreateInputSchema>, mallId: string) => Promise<ApiResponse<Floor>>;
export declare const getFloor: (floorId: string) => Promise<ApiResponse<Floor | null>>;
export declare const getFloors: (mallId: string) => Promise<ApiResponse<Floor[]>>;
export declare const updateFloor: (input: Partial<z.infer<typeof FloorCreateInputSchema>>, floorId: string) => Promise<ApiResponse<Floor>>;
export declare const deleteFloor: (floorId: string) => Promise<ApiResponse<{
    message: string;
}>>;
export {};
