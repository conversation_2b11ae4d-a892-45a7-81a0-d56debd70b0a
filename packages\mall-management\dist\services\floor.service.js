"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteFloor = exports.updateFloor = exports.getFloors = exports.getFloor = exports.createFloor = void 0;
const core_1 = require("@mallsurf/core");
const zod_1 = require("zod");
// Define FloorCreateInputSchema locally
const FloorCreateInputSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    level: zod_1.z.number().int(),
    description: zod_1.z.string().optional(),
    mapImage: zod_1.z.string().url().optional(),
    isActive: zod_1.z.boolean().default(true),
});
const createFloor = async (input, mallId) => {
    const floor = await core_1.prisma.floor.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
        },
    });
    return {
        success: true,
        data: floor,
    };
};
exports.createFloor = createFloor;
const getFloor = async (floorId) => {
    const floor = await core_1.prisma.floor.findUnique({
        where: { id: floorId },
        include: {
            shops: true,
        },
    });
    return {
        success: true,
        data: floor,
    };
};
exports.getFloor = getFloor;
const getFloors = async (mallId) => {
    const floors = await core_1.prisma.floor.findMany({
        where: { mallId },
    });
    return {
        success: true,
        data: floors,
    };
};
exports.getFloors = getFloors;
const updateFloor = async (input, floorId) => {
    const floor = await core_1.prisma.floor.update({
        where: { id: floorId },
        data: input,
    });
    return {
        success: true,
        data: floor,
    };
};
exports.updateFloor = updateFloor;
const deleteFloor = async (floorId) => {
    await core_1.prisma.floor.delete({
        where: { id: floorId },
    });
    return {
        success: true,
        data: { message: "Floor deleted" },
    };
};
exports.deleteFloor = deleteFloor;
