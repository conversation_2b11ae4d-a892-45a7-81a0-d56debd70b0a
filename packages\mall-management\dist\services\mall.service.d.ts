import { Mall, MallHour, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const MallCreateInputSchema: z.ZodObject<{
    name: z.ZodString;
    slug: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    address: z.ZodString;
    city: z.ZodString;
    state: z.ZodString;
    zipCode: z.ZodString;
    country: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    email: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodString>;
    logo: z.ZodOptional<z.ZodString>;
    images: z.<PERSON>odOptional<z.Zod<PERSON><PERSON>y<z.ZodString, "many">>;
    isActive: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name: string;
    slug: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    isActive: boolean;
    description?: string | undefined;
    phone?: string | undefined;
    email?: string | undefined;
    website?: string | undefined;
    logo?: string | undefined;
    images?: string[] | undefined;
}, {
    name: string;
    slug: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    description?: string | undefined;
    phone?: string | undefined;
    email?: string | undefined;
    website?: string | undefined;
    logo?: string | undefined;
    images?: string[] | undefined;
    isActive?: boolean | undefined;
}>;
export declare const getMalls: (page: number, limit: number, city?: string, search?: string, isActive?: boolean) => Promise<ApiResponse<Mall[]>>;
export declare const getMall: (mallId: string) => Promise<ApiResponse<Mall | null>>;
export declare const createMall: (input: z.infer<typeof MallCreateInputSchema>, userId: string) => Promise<ApiResponse<Mall>>;
export declare const updateMall: (input: Partial<z.infer<typeof MallCreateInputSchema>>, mallId: string) => Promise<ApiResponse<Mall>>;
export declare const deleteMall: (mallId: string) => Promise<ApiResponse<{
    message: string;
}>>;
export declare const getMallHours: (mallId: string) => Promise<ApiResponse<MallHour[]>>;
export declare const createMallHour: (input: {
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
}, mallId: string) => Promise<ApiResponse<MallHour>>;
export declare const updateMallHour: (input: Partial<{
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
}>, hourId: string) => Promise<ApiResponse<MallHour>>;
export declare const deleteMallHour: (hourId: string) => Promise<ApiResponse<{
    message: string;
}>>;
