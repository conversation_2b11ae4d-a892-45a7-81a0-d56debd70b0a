"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteMallHour = exports.updateMallHour = exports.createMallHour = exports.getMallHours = exports.deleteMall = exports.updateMall = exports.createMall = exports.getMall = exports.getMalls = exports.MallCreateInputSchema = void 0;
const core_1 = require("@mallsurf/core");
const zod_1 = require("zod");
// Note: MallCreateInputSchema should be defined here or imported from a schema file
exports.MallCreateInputSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    slug: zod_1.z.string().min(1),
    description: zod_1.z.string().optional(),
    address: zod_1.z.string().min(1),
    city: zod_1.z.string().min(1),
    state: zod_1.z.string().min(1),
    zipCode: zod_1.z.string().min(1),
    country: zod_1.z.string().min(1),
    phone: zod_1.z.string().optional(),
    email: zod_1.z.string().email().optional(),
    website: zod_1.z.string().url().optional(),
    logo: zod_1.z.string().url().optional(),
    images: zod_1.z.array(zod_1.z.string().url()).optional(),
    isActive: zod_1.z.boolean().default(true),
});
const getMalls = async (page, limit, city, search, isActive) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const malls = await core_1.prisma.mall.findMany({
        skip,
        take,
        where: {
            name: {
                contains: search,
            },
            slug: {
                contains: search,
            },
            ...(city && { city }),
            ...(isActive != undefined && { isActive }),
        }
    });
    const total = await core_1.prisma.mall.count({
        where: {
            name: {
                contains: search,
            },
            slug: {
                contains: search,
            },
            ...(city && { city }),
            ...(isActive != undefined && { isActive }),
        }
    });
    return {
        success: true,
        data: malls,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getMalls = getMalls;
const getMall = async (mallId) => {
    const mall = await core_1.prisma.mall.findUnique({
        where: { id: mallId },
        include: {
            user: {
                select: {
                    id: true,
                    name: true,
                    email: true,
                }
            },
            floors: true,
            events: true,
            offers: true,
        },
    });
    return {
        success: true,
        data: mall,
    };
};
exports.getMall = getMall;
const createMall = async (input, userId) => {
    const mall = await core_1.prisma.mall.create({
        data: {
            ...input,
            user: {
                connect: {
                    id: userId
                }
            }
        },
    });
    return {
        success: true,
        data: mall,
    };
};
exports.createMall = createMall;
const updateMall = async (input, mallId) => {
    const mall = await core_1.prisma.mall.update({
        where: { id: mallId },
        data: input,
    });
    return {
        success: true,
        data: mall,
    };
};
exports.updateMall = updateMall;
const deleteMall = async (mallId) => {
    await core_1.prisma.mall.delete({
        where: { id: mallId },
    });
    return {
        success: true,
        data: { message: "Mall deleted" },
    };
};
exports.deleteMall = deleteMall;
const getMallHours = async (mallId) => {
    const hours = await core_1.prisma.mallHour.findMany({
        where: { mallId },
    });
    return {
        success: true,
        data: hours,
    };
};
exports.getMallHours = getMallHours;
const createMallHour = async (input, mallId) => {
    const hour = await core_1.prisma.mallHour.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
        },
    });
    return {
        success: true,
        data: hour,
    };
};
exports.createMallHour = createMallHour;
const updateMallHour = async (input, hourId) => {
    const hour = await core_1.prisma.mallHour.update({
        where: { id: hourId },
        data: input,
    });
    return {
        success: true,
        data: hour,
    };
};
exports.updateMallHour = updateMallHour;
const deleteMallHour = async (hourId) => {
    await core_1.prisma.mallHour.delete({
        where: { id: hourId },
    });
    return {
        success: true,
        data: { message: "Mall hour deleted" },
    };
};
exports.deleteMallHour = deleteMallHour;
