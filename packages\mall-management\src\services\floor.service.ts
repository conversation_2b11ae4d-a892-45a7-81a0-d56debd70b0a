import { Floor, prisma, ApiResponse } from "@mallsurf/core";
import { z } from "zod";

// Define FloorCreateInputSchema locally
const FloorCreateInputSchema = z.object({
    name: z.string().min(1),
    level: z.number().int(),
    description: z.string().optional(),
    mapImage: z.string().url().optional(),
    isActive: z.boolean().default(true),
});

export const createFloor = async (
    input: z.infer<typeof FloorCreateInputSchema>,
    mallId: string
): Promise<ApiResponse<Floor>> => {
    const floor = await prisma.floor.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
        },
    });

    return {
        success: true,
        data: floor,
    };
}

export const getFloor = async (floorId: string): Promise<ApiResponse<Floor | null>> => {
    const floor = await prisma.floor.findUnique({
        where: { id: floorId },
        include: {
            shops: true,
        },

    });

    return {
        success: true,
        data: floor,
    };
}

export const getFloors = async (mallId: string): Promise<ApiResponse<Floor[]>> => {
    const floors = await prisma.floor.findMany({
        where: { mallId },
    });

    return {
        success: true,
        data: floors,
    };
}

export const updateFloor = async (
    input: Partial<z.infer<typeof FloorCreateInputSchema>>,
    floorId: string
): Promise<ApiResponse<Floor>> => {
    const floor = await prisma.floor.update({
        where: { id: floorId },
        data: input,
    });

    return {
        success: true,
        data: floor,
    };
}

export const deleteFloor = async (floorId: string): Promise<ApiResponse<{ message: string }>> => {
    await prisma.floor.delete({
        where: { id: floorId },
    });

    return {
        success: true,
        data: { message: "Floor deleted" },
    };
}