import { Offer, OfferCreateInputSchema, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const createOffer: (input: z.infer<typeof OfferCreateInputSchema>, mallId: string, userId: string) => Promise<ApiResponse<Offer>>;
export declare const getOffer: (offerId: string) => Promise<ApiResponse<Offer | null>>;
export declare const getOffers: (mallId: string, page: number, limit: number, shopId?: string, validFrom?: Date, validTo?: Date) => Promise<ApiResponse<Offer[]>>;
export declare const updateOffer: (input: Partial<z.infer<typeof OfferCreateInputSchema>>, offerId: string, userId: string) => Promise<ApiResponse<Offer>>;
export declare const deleteOffer: (offerId: string) => Promise<ApiResponse<{
    message: string;
}>>;
