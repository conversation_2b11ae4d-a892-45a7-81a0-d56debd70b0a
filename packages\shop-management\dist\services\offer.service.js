"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteOffer = exports.updateOffer = exports.getOffers = exports.getOffer = exports.createOffer = void 0;
const core_1 = require("@mallsurf/core");
const createOffer = async (input, mallId, userId) => {
    const offer = await core_1.prisma.offer.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });
    return {
        success: true,
        data: offer,
    };
};
exports.createOffer = createOffer;
const getOffer = async (offerId) => {
    const offer = await core_1.prisma.offer.findUnique({
        where: { id: offerId },
        include: {
            shopOffers: true,
        },
    });
    return {
        success: true,
        data: offer,
    };
};
exports.getOffer = getOffer;
const getOffers = async (mallId, page, limit, shopId, validFrom, validTo) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const offers = await core_1.prisma.offer.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(shopId && { shopOffers: { some: { shopId } } }),
            ...(validFrom && { validFrom: { gte: validFrom } }),
            ...(validTo && { validTo: { lte: validTo } }),
        }
    });
    const total = await core_1.prisma.offer.count({
        where: {
            mallId,
            ...(shopId && { shopOffers: { some: { shopId } } }),
            ...(validFrom && { validFrom: { gte: validFrom } }),
            ...(validTo && { validTo: { lte: validTo } }),
        }
    });
    return {
        success: true,
        data: offers,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getOffers = getOffers;
const updateOffer = async (input, offerId, userId) => {
    const offer = await core_1.prisma.offer.update({
        where: { id: offerId },
        data: {
            ...input,
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });
    return {
        success: true,
        data: offer,
    };
};
exports.updateOffer = updateOffer;
const deleteOffer = async (offerId) => {
    await core_1.prisma.offer.delete({
        where: { id: offerId },
    });
    return {
        success: true,
        data: { message: "Offer deleted" },
    };
};
exports.deleteOffer = deleteOffer;
