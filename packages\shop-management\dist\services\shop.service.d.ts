import { Shop, ShopHour, ApiResponse } from "@mallsurf/core";
import { z } from "zod";
export declare const ShopCreateInputSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    category: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    email: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodString>;
    logo: z.ZodOptional<z.ZodString>;
    images: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    floorId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    category: string;
    isActive: boolean;
    description?: string | undefined;
    phone?: string | undefined;
    email?: string | undefined;
    website?: string | undefined;
    logo?: string | undefined;
    images?: string[] | undefined;
    floorId?: string | undefined;
}, {
    name: string;
    category: string;
    description?: string | undefined;
    phone?: string | undefined;
    email?: string | undefined;
    website?: string | undefined;
    logo?: string | undefined;
    images?: string[] | undefined;
    isActive?: boolean | undefined;
    floorId?: string | undefined;
}>;
export declare const createShop: (input: z.infer<typeof ShopCreateInputSchema>, mallId: string, userId: string) => Promise<ApiResponse<Shop>>;
export declare const getShops: (mallId: string, page: number, limit: number, floorId?: string, category?: string, search?: string) => Promise<ApiResponse<Shop[]>>;
export declare const getShop: (shopId: string) => Promise<ApiResponse<Shop | null>>;
export declare const updateShop: (input: Partial<z.infer<typeof ShopCreateInputSchema>>, shopId: string) => Promise<ApiResponse<Shop>>;
export declare const deleteShop: (shopId: string) => Promise<ApiResponse<{
    message: string;
}>>;
export declare const getShopHours: (shopId: string) => Promise<ApiResponse<ShopHour[]>>;
export declare const createShopHour: (input: {
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
}, shopId: string) => Promise<ApiResponse<ShopHour>>;
export declare const updateShopHour: (input: Partial<{
    day: number;
    open: string;
    close: string;
    isClosed: boolean;
}>, hourId: string) => Promise<ApiResponse<ShopHour>>;
export declare const deleteShopHour: (hourId: string) => Promise<ApiResponse<{
    message: string;
}>>;
