"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteShopHour = exports.updateShopHour = exports.createShopHour = exports.getShopHours = exports.deleteShop = exports.updateShop = exports.getShop = exports.getShops = exports.createShop = exports.ShopCreateInputSchema = void 0;
const core_1 = require("@mallsurf/core");
const zod_1 = require("zod");
// Define ShopCreateInputSchema locally
exports.ShopCreateInputSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    description: zod_1.z.string().optional(),
    category: zod_1.z.string().min(1),
    phone: zod_1.z.string().optional(),
    email: zod_1.z.string().email().optional(),
    website: zod_1.z.string().url().optional(),
    logo: zod_1.z.string().url().optional(),
    images: zod_1.z.array(zod_1.z.string().url()).optional(),
    isActive: zod_1.z.boolean().default(true),
    floorId: zod_1.z.string().optional(),
});
const createShop = async (input, mallId, userId) => {
    const shop = await core_1.prisma.shop.create({
        data: {
            ...input,
            mall: {
                connect: {
                    id: mallId,
                }
            },
            user: {
                connect: {
                    id: userId,
                }
            },
        },
    });
    return {
        success: true,
        data: shop,
    };
};
exports.createShop = createShop;
const getShops = async (mallId, page, limit, floorId, category, search) => {
    const skip = (page - 1) * limit;
    const take = limit;
    const shops = await core_1.prisma.shop.findMany({
        skip,
        take,
        where: {
            mallId,
            ...(floorId && { floorId }),
            ...(category && { category }),
            name: {
                contains: search,
            },
        }
    });
    const total = await core_1.prisma.shop.count({
        where: {
            mallId,
            ...(floorId && { floorId }),
            ...(category && { category }),
            name: {
                contains: search,
            },
        }
    });
    return {
        success: true,
        data: shops,
        meta: {
            page,
            limit,
            total,
            hasNext: (page * limit) < total,
        }
    };
};
exports.getShops = getShops;
const getShop = async (shopId) => {
    const shop = await core_1.prisma.shop.findUnique({
        where: { id: shopId },
        include: {
            shopHours: true,
            shopOffers: true,
        },
    });
    return {
        success: true,
        data: shop,
    };
};
exports.getShop = getShop;
const updateShop = async (input, shopId) => {
    const shop = await core_1.prisma.shop.update({
        where: { id: shopId },
        data: input,
    });
    return {
        success: true,
        data: shop,
    };
};
exports.updateShop = updateShop;
const deleteShop = async (shopId) => {
    await core_1.prisma.shop.delete({
        where: { id: shopId },
    });
    return {
        success: true,
        data: { message: "Shop deleted" },
    };
};
exports.deleteShop = deleteShop;
const getShopHours = async (shopId) => {
    const hours = await core_1.prisma.shopHour.findMany({
        where: { shopId },
    });
    return {
        success: true,
        data: hours,
    };
};
exports.getShopHours = getShopHours;
const createShopHour = async (input, shopId) => {
    const hour = await core_1.prisma.shopHour.create({
        data: {
            ...input,
            shop: {
                connect: {
                    id: shopId,
                }
            },
        },
    });
    return {
        success: true,
        data: hour,
    };
};
exports.createShopHour = createShopHour;
const updateShopHour = async (input, hourId) => {
    const hour = await core_1.prisma.shopHour.update({
        where: { id: hourId },
        data: input,
    });
    return {
        success: true,
        data: hour,
    };
};
exports.updateShopHour = updateShopHour;
const deleteShopHour = async (hourId) => {
    await core_1.prisma.shopHour.delete({
        where: { id: hourId },
    });
    return {
        success: true,
        data: { message: "Shop hour deleted" },
    };
};
exports.deleteShopHour = deleteShopHour;
