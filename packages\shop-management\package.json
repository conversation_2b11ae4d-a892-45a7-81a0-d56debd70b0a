{"name": "@mallsurf/shop-management", "version": "1.0.0", "private": false, "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"registry": "https://npm.pkg.github.com", "@mallsurf:registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/mallsurf-packages/mallsurf-shop-management.git"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "dependencies": {"@mallsurf/core": "^1.0.0", "@mallsurf/auth": "^1.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20", "typescript": "^5", "rimraf": "^5.0.0"}, "files": ["dist/**/*", "README.md"], "keywords": ["mallsurf", "shop", "management"]}