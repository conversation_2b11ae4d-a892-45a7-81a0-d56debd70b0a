#!/bin/bash

# Create initial releases for all packages
# This will trigger the publishing workflows

set -e

echo "🏷️  Creating initial releases for all packages..."

# Array of packages in dependency order (important!)
declare -a packages_ordered=(
    "mallsurf-core"
    "mallsurf-auth"
    "mallsurf-mall-management"
    "mallsurf-shop-management"
    "mallsurf-analytics"
    "mallsurf-events"
)

cd extracted-packages

for repo_name in "${packages_ordered[@]}"; do
    echo "🔖 Creating release for $repo_name..."
    
    if [ -d "$repo_name" ]; then
        cd "$repo_name"
        
        # Create and push v1.0.0 tag
        git tag -a v1.0.0 -m "Initial release v1.0.0"
        git push origin v1.0.0
        
        echo "✅ Created v1.0.0 release for: $repo_name"
        
        # Wait a bit between releases to avoid overwhelming GitHub Actions
        echo "⏳ Waiting 30 seconds before next release..."
        sleep 30
        
        cd ..
    else
        echo "⚠️  Directory $repo_name not found, skipping..."
    fi
done

cd ..

echo ""
echo "🎉 All initial releases created!"
echo ""
echo "📋 Monitor the releases:"
echo "1. Check GitHub Actions workflows are running"
echo "2. Verify packages are published to GitHub Packages"
echo "3. Test package installation from the registry"
echo ""
echo "🔗 GitHub Packages: https://github.com/orgs/mallsurf-packages/packages"
