#!/bin/bash

# MallSurf Multi-Repo Setup Script
# This script creates all the individual repositories for the MallSurf packages

set -e

echo "🚀 Creating MallSurf Multi-Repo Structure..."

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed. Please install it first:"
    echo "   https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI. Please run:"
    echo "   gh auth login"
    exit 1
fi

# Organization name
ORG="Mallsurf"

echo "📋 Creating repositories in organization: $ORG"

# Array of repositories to create
declare -a repos=(
    "mallsurf-core:@mallsurf/core - Foundation package with database, types, and utilities"
    "mallsurf-auth:@mallsurf/auth - Authentication and user management"
    "mallsurf-mall-management:@mallsurf/mall-management - Mall operations and management"
    "mallsurf-shop-management:@mallsurf/shop-management - Shop operations and management"
    "mallsurf-analytics:@mallsurf/analytics - Reporting and analytics"
    "mallsurf-events:@mallsurf/events - Event management"
    "mallsurf-app:MallSurf main application - Next.js application"
)

# Create each repository
for repo_info in "${repos[@]}"; do
    IFS=':' read -r repo_name description <<< "$repo_info"
    
    echo "📦 Creating repository: $repo_name"
    
    # Create the repository
    gh repo create "$ORG/$repo_name" \
        --private \
        --description "$description" \
        --add-readme \
        --gitignore "Node" \
        --license "MIT"
    
    echo "✅ Created: https://github.com/$ORG/$repo_name"
done

echo ""
echo "🎉 All repositories created successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Configure repository access controls"
echo "2. Set up branch protection rules"
echo "3. Add repository secrets for CI/CD"
echo ""
echo "🔗 Organization URL: https://github.com/orgs/$ORG"
