#!/bin/bash

# MallSurf Package Migration Script
# This script extracts each package from the monorepo with full git history
# and prepares them for migration to separate repositories

set -e

echo "🚀 Starting MallSurf package migration..."

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Not in a git repository. Please run this script from the root of your git repository."
    exit 1
fi

# Create a backup branch
echo "📋 Creating backup branch..."
git checkout -b migration-backup-$(date +%Y%m%d-%H%M%S)
git checkout main

# Array of packages to migrate
declare -A packages=(
    ["core"]="mallsurf-core"
    ["auth"]="mallsurf-auth"
    ["mall-management"]="mallsurf-mall-management"
    ["shop-management"]="mallsurf-shop-management"
    ["analytics"]="mallsurf-analytics"
    ["events"]="mallsurf-events"
)

# Create extraction directory
mkdir -p extracted-packages
cd extracted-packages

echo "📦 Extracting packages with git history..."

for package in "${!packages[@]}"; do
    repo_name="${packages[$package]}"
    
    echo "🔄 Processing package: $package -> $repo_name"
    
    # Clone the original repository
    git clone .. "$repo_name"
    cd "$repo_name"
    
    # Extract only the package directory with history
    git filter-branch --prune-empty --subdirectory-filter "packages/$package" HEAD
    
    # Clean up the extracted repository
    git reset --hard
    git gc --aggressive
    git prune
    
    # Add README if it doesn't exist
    if [ ! -f "README.md" ]; then
        cat > README.md << EOF
# @mallsurf/$package

This package is part of the MallSurf application suite.

## Installation

\`\`\`bash
npm install @mallsurf/$package
\`\`\`

## Usage

\`\`\`javascript
import { ... } from '@mallsurf/$package';
\`\`\`

## Development

\`\`\`bash
# Install dependencies
npm install

# Build the package
npm run build

# Watch for changes
npm run dev
\`\`\`

## Publishing

This package is automatically published to GitHub Packages when a new tag is created.

\`\`\`bash
# Create a new version
npm version patch|minor|major

# Push the tag
git push origin --tags
\`\`\`
EOF
    fi
    
    # Add .gitignore if it doesn't exist
    if [ ! -f ".gitignore" ]; then
        cat > .gitignore << EOF
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Coverage
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
EOF
    fi
    
    # Commit the new files
    git add .
    git commit -m "chore: add README and .gitignore for standalone repository" || true
    
    echo "✅ Extracted: $repo_name"
    cd ..
done

cd ..

echo ""
echo "🎉 Package extraction completed!"
echo ""
echo "📁 Extracted packages are in: ./extracted-packages/"
echo ""
echo "📋 Next steps:"
echo "1. Create repositories on GitHub:"
for package in "${!packages[@]}"; do
    repo_name="${packages[$package]}"
    echo "   gh repo create mallsurf-packages/$repo_name --private"
done
echo ""
echo "2. Push extracted packages to their repositories:"
for package in "${!packages[@]}"; do
    repo_name="${packages[$package]}"
    echo "   cd extracted-packages/$repo_name"
    echo "   git remote <NAME_EMAIL>:mallsurf-packages/$repo_name.git"
    echo "   git push -u origin main"
    echo "   cd ../.."
done
echo ""
echo "3. Set up repository access controls and branch protection"
echo "4. Test package publishing workflows"
