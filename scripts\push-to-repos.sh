#!/bin/bash

# Push extracted packages to their respective GitHub repositories
# Run this script after creating the repositories on GitHub

set -e

echo "🚀 Pushing packages to GitHub repositories..."

# Check if extracted-packages directory exists
if [ ! -d "extracted-packages" ]; then
    echo "❌ extracted-packages directory not found. Please run migrate-packages.sh first."
    exit 1
fi

# Array of packages
declare -A packages=(
    ["core"]="mallsurf-core"
    ["auth"]="mallsurf-auth"
    ["mall-management"]="mallsurf-mall-management"
    ["shop-management"]="mallsurf-shop-management"
    ["analytics"]="mallsurf-analytics"
    ["events"]="mallsurf-events"
)

cd extracted-packages

for package in "${!packages[@]}"; do
    repo_name="${packages[$package]}"
    
    echo "📦 Pushing $repo_name to GitHub..."
    
    if [ -d "$repo_name" ]; then
        cd "$repo_name"
        
        # Add remote if it doesn't exist
        if ! git remote get-url origin > /dev/null 2>&1; then
            git remote add origin "**************:mallsurf-packages/$repo_name.git"
        fi
        
        # Push to GitHub
        git push -u origin main
        
        # Push tags if any
        git push origin --tags || true
        
        echo "✅ Pushed: $repo_name"
        cd ..
    else
        echo "⚠️  Directory $repo_name not found, skipping..."
    fi
done

cd ..

echo ""
echo "🎉 All packages pushed to GitHub!"
echo ""
echo "📋 Next steps:"
echo "1. Verify repositories on GitHub"
echo "2. Set up access controls for each repository"
echo "3. Configure branch protection rules"
echo "4. Test CI/CD workflows"
echo "5. Create initial releases with tags"
