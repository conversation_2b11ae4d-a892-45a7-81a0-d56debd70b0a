# Repository Access Control Setup

## Overview
This document outlines how to configure access controls for each repository in the mallsurf-packages organization.

## Access Control Strategy

### Repository Permissions

| Repository | Owner (You) | Junior Dev | Mid-Level Dev | Integration Team |
|------------|-------------|------------|---------------|------------------|
| mallsurf-core | Admin | - | - | Read |
| mallsurf-auth | Admin | Write | - | Read |
| mallsurf-mall-management | Admin | - | Write | Read |
| mallsurf-shop-management | Admin | - | Write | Read |
| mallsurf-analytics | Admin | Write | - | Read |
| mallsurf-events | Admin | Write | - | Read |
| mallsurf-app | Admin | Write | Write | Write |

## Setting Up Access Controls

### 1. Organization Settings
```bash
# Navigate to organization settings
https://github.com/orgs/mallsurf-packages/settings/member_privileges

# Configure:
# - Base permissions: "No permission"
# - Repository creation: "Private" only
# - Repository deletion and transfer: "Admin only"
```

### 2. Repository-Specific Access

#### For mallsurf-core (Most Sensitive)
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-core/settings/access

# Access:
# - Owner: Admin access
# - Integration Team: Read access (for consuming the package)
# - No other access granted
```

#### For mallsurf-auth
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-auth/settings/access

# Access:
# - Owner: Admin access
# - Junior Developer (Auth): Write access
# - Integration Team: Read access
```

#### For mallsurf-mall-management
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-mall-management/settings/access

# Access:
# - Owner: Admin access
# - Mid-Level Developer (Mall): Write access
# - Integration Team: Read access
```

#### For mallsurf-shop-management
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-shop-management/settings/access

# Access:
# - Owner: Admin access
# - Mid-Level Developer (Shop): Write access
# - Integration Team: Read access
```

#### For mallsurf-analytics
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-analytics/settings/access

# Access:
# - Owner: Admin access
# - Junior-Mid Developer (Analytics): Write access
# - Integration Team: Read access
```

#### For mallsurf-events
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-events/settings/access

# Access:
# - Owner: Admin access
# - Junior Developer (Events): Write access
# - Integration Team: Read access
```

#### For mallsurf-app
```bash
# Repository URL: https://github.com/mallsurf-packages/mallsurf-app/settings/access

# Access:
# - Owner: Admin access
# - All developers: Write access (for integration)
```

## Branch Protection Rules

### Standard Protection for All Repositories
```yaml
# Settings → Branches → Add rule
Branch name pattern: main

Protection rules:
✅ Require a pull request before merging
  ✅ Require approvals: 1
  ✅ Dismiss stale PR approvals when new commits are pushed
  ✅ Require review from code owners
✅ Require status checks to pass before merging
  ✅ Require branches to be up to date before merging
✅ Require conversation resolution before merging
✅ Include administrators
```

### Enhanced Protection for Core Package
```yaml
# Additional rules for mallsurf-core
✅ Restrict pushes that create files
✅ Require signed commits
✅ Require deployments to succeed before merging
```

## Team Management

### Creating Teams
1. Go to `https://github.com/orgs/mallsurf-packages/teams`
2. Create teams:
   - `core-maintainers` (Owner only)
   - `auth-developers` (Owner + Auth developer)
   - `mall-developers` (Owner + Mall developer)
   - `shop-developers` (Owner + Shop developer)
   - `analytics-developers` (Owner + Analytics developer)
   - `events-developers` (Owner + Events developer)
   - `integration-team` (All developers with read access)

### Assigning Team Permissions
```bash
# Example: Add team to repository
gh api repos/mallsurf-packages/mallsurf-auth/teams/auth-developers \
  --method PUT \
  --field permission=push
```

## Security Settings

### Repository Secrets
Each repository needs these secrets for CI/CD:
- `GITHUB_TOKEN` (automatically provided)
- `NPM_TOKEN` (for publishing to npm if needed)

### Organization Secrets
Set up organization-level secrets:
- `GITHUB_PACKAGES_TOKEN`
- `SLACK_WEBHOOK` (for notifications)

## Monitoring and Auditing

### Regular Access Reviews
- Monthly review of repository access
- Quarterly review of organization membership
- Annual review of permissions and teams

### Audit Logging
- Enable organization audit log
- Monitor package downloads
- Track repository access patterns

## Emergency Procedures

### Revoking Access
```bash
# Remove user from organization
gh api orgs/mallsurf-packages/members/USERNAME --method DELETE

# Remove user from specific repository
gh api repos/mallsurf-packages/REPO/collaborators/USERNAME --method DELETE
```

### Incident Response
1. Immediately revoke compromised access
2. Review audit logs
3. Rotate affected tokens/secrets
4. Notify team members
5. Document incident

## Implementation Checklist

- [ ] Create GitHub organization
- [ ] Set organization base permissions
- [ ] Create all repositories
- [ ] Configure repository access controls
- [ ] Set up branch protection rules
- [ ] Create teams and assign members
- [ ] Configure repository secrets
- [ ] Test access controls
- [ ] Document team member onboarding process
