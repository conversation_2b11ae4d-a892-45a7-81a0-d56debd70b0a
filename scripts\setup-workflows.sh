#!/bin/bash

# Setup GitHub Actions workflows for all packages
# This script copies the workflow templates to each package directory

set -e

echo "🔧 Setting up GitHub Actions workflows for all packages..."

# Array of package directories
declare -a packages=(
    "packages/core"
    "packages/auth"
    "packages/mall-management"
    "packages/shop-management"
    "packages/analytics"
    "packages/events"
)

# Create workflows for each package
for package_dir in "${packages[@]}"; do
    if [ -d "$package_dir" ]; then
        echo "📦 Setting up workflows for $package_dir"
        
        # Create .github/workflows directory
        mkdir -p "$package_dir/.github/workflows"
        
        # Copy workflow templates
        cp "templates/.github/workflows/ci.yml" "$package_dir/.github/workflows/"
        cp "templates/.github/workflows/publish.yml" "$package_dir/.github/workflows/"
        
        # Copy dependency update workflow only for packages that depend on others
        if [[ "$package_dir" != "packages/core" ]]; then
            cp "templates/.github/workflows/dependency-update.yml" "$package_dir/.github/workflows/"
        fi
        
        echo "✅ Workflows set up for $package_dir"
    else
        echo "⚠️  Directory $package_dir not found, skipping..."
    fi
done

echo ""
echo "🎉 All workflows set up successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Commit and push the workflow files"
echo "2. Create tags to trigger publishing workflows"
echo "3. Monitor workflow runs in GitHub Actions"
