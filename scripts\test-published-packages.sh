#!/bin/bash

# Test the main application with published packages
# This script helps verify that the migration was successful

set -e

echo "🧪 Testing main application with published packages..."

# Check if GITHUB_TOKEN is set
if [ -z "$GITHUB_TOKEN" ]; then
    echo "❌ GITHUB_TOKEN environment variable is not set."
    echo "Please set it with your GitHub personal access token:"
    echo "export GITHUB_TOKEN=your_token_here"
    exit 1
fi

# Backup current node_modules and package-lock.json
echo "📋 Creating backup of current dependencies..."
if [ -d "node_modules" ]; then
    mv node_modules node_modules.backup
fi
if [ -f "package-lock.json" ]; then
    mv package-lock.json package-lock.json.backup
fi

# Clean install with published packages
echo "📦 Installing published packages..."
npm install

# Verify packages are installed from GitHub Packages
echo "🔍 Verifying package sources..."
npm list @mallsurf/core @mallsurf/auth @mallsurf/mall-management @mallsurf/shop-management @mallsurf/analytics @mallsurf/events

# Try to build the application
echo "🏗️  Building application..."
npm run build

echo ""
echo "✅ Success! Main application works with published packages."
echo ""
echo "📋 Next steps:"
echo "1. Test the application thoroughly"
echo "2. Update CI/CD to use published packages"
echo "3. Remove the packages/ directory from the main repo"
echo "4. Update documentation"

# Function to restore backup if needed
restore_backup() {
    echo "🔄 Restoring backup..."
    rm -rf node_modules
    rm -f package-lock.json
    if [ -d "node_modules.backup" ]; then
        mv node_modules.backup node_modules
    fi
    if [ -f "package-lock.json.backup" ]; then
        mv package-lock.json.backup package-lock.json
    fi
    echo "✅ Backup restored."
}

# Ask user if they want to keep the new setup or restore backup
echo ""
read -p "Keep the new published package setup? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    restore_backup
    echo "Reverted to workspace dependencies."
else
    # Clean up backup files
    rm -rf node_modules.backup
    rm -f package-lock.json.backup
    echo "✅ Published package setup confirmed!"
fi
