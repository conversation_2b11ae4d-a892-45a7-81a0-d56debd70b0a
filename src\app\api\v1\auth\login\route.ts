
import { errorHand<PERSON> } from "@mallsurf/core";
import { validate } from "@/lib/middleware/validation";
import { NextRequest, NextResponse } from "next/server";
import { LoginInputSchema, loginUser, LoginInput } from "@mallsurf/auth";
import { setAccessToken } from "@/services/cookie.service";

export async function POST(request: NextRequest) {
    try {
        const body = (await request.json()) as LoginInput;
        const validatedData = await validate(LoginInputSchema, body);
        const result = await loginUser(validatedData);
        const { accessToken } = result.data as { accessToken: string };
        await setAccessToken(accessToken);
        return NextResponse.json(result, { status: 200 });
    } catch (error) {
        return errorHandler(error);
    }
}