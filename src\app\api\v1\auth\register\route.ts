import { errorHandler } from "@mallsurf/core";
import { RegisterInput, RegisterInputSchema, registerUser } from "@mallsurf/auth";
import { validate } from "@/lib/middleware/validation";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as RegisterInput;
    const validatedData = await validate(RegisterInputSchema, body);
    const result = await registerUser(validatedData);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    return errorHandler(error);
  }
}