import { User<PERSON><PERSON>, errorH<PERSON><PERSON>, FloorCreateInputSchema } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { deleteFloor, getFloor, updateFloor } from "@mallsurf/mall-management";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string, floorId: string }> }) {
    try {
        const { floorId } = await params;
        const result = await getFloor(floorId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string, floorId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { floorId } = await params;
        const body = (await request.json()) as Partial<z.infer<typeof FloorCreateInputSchema>>;
        const result = await updateFloor(body, floorId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string, floorId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { floorId } = await params;
        const result = await deleteFloor(floorId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}
