import { User<PERSON><PERSON>, error<PERSON><PERSON><PERSON>, FloorCreateInputSchema } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { createFloor, getFloors } from "@mallsurf/mall-management";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const result = await getFloors(id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const body = (await request.json()) as z.infer<typeof FloorCreateInputSchema>;
        const result = await createFloor(body, id);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}