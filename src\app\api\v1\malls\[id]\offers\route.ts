import { error<PERSON><PERSON><PERSON>, UserRole, OfferCreateInputSchema } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { createOffer, getOffers } from "@mallsurf/shop-management";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const shopId = request.nextUrl.searchParams.get('shopId') || undefined;
        const validFrom = request.nextUrl.searchParams.get('validFrom') ? new Date(request.nextUrl.searchParams.get('validFrom')!) : undefined;
        const validTo = request.nextUrl.searchParams.get('validTo') ? new Date(request.nextUrl.searchParams.get('validTo')!) : undefined;
        const result = await getOffers(id, page, limit, shopId, validFrom, validTo);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const body = (await request.json()) as z.infer<typeof OfferCreateInputSchema>;
        const result = await createOffer(body, id, decoded.userId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}