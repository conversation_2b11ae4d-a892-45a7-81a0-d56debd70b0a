import { UserR<PERSON>, errorH<PERSON><PERSON> } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { deleteMall, getMall, updateMall, MallCreateInputSchema } from "@mallsurf/mall-management"
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const result = await getMall(id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const body = (await request.json()) as Partial<z.infer<typeof MallCreateInputSchema>>;
        const result = await updateMall(body, id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const result = await deleteMall(id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}