import { errorHand<PERSON>, UserRole, ShopCreateInputSchema } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { deleteShop, getShop, updateShop } from "@mallsurf/shop-management";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string, shopId: string }> }) {
    try {
        const { shopId } = await params;
        const result = await getShop(shopId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string, shopId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { shopId } = await params;
        const body = (await request.json()) as Partial<z.infer<typeof ShopCreateInputSchema>>;
        const result = await updateShop(body, shopId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string, shopId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { shopId } = await params;
        const result = await deleteShop(shopId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}
