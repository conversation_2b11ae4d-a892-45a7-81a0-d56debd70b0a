import { error<PERSON><PERSON><PERSON>, UserR<PERSON>, ShopCreateInputSchema } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { createShop, getShops } from "@mallsurf/shop-management";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const floorId = request.nextUrl.searchParams.get('floorId') || undefined;
        const category = request.nextUrl.searchParams.get('category') || undefined;
        const search = request.nextUrl.searchParams.get('search') || undefined;
        const result = await getShops(id, page, limit, floorId, category, search);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { id } = await params;
        const body = (await request.json()) as z.infer<typeof ShopCreateInputSchema>;
        const result = await createShop(body, id, decoded.userId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}