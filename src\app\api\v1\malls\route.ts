import { errorH<PERSON><PERSON> } from "@/lib/api-error";
import { createMall, getMalls, MallCreateInputSchema } from "@mallsurf/mall-management"
import { authMiddleware } from "@mallsurf/auth"
import { UserRole } from "@mallsurf/core"
import { validate } from "@/lib/middleware/validation";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
    try {
        const page = Number(request.nextUrl.searchParams.get('page')) || 1;
        const limit = Number(request.nextUrl.searchParams.get('limit')) || 10;
        const search = request.nextUrl.searchParams.get('search') || undefined;
        const city = request.nextUrl.searchParams.get('city') || undefined;
        const isActive = request.nextUrl.searchParams.get('isActive') === 'true';

        const result = await getMalls(page, limit, city, search, isActive);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function POST(request: NextRequest) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const body = (await request.json())
        const validatedData = await validate(MallCreateInputSchema, body);
        const result = await createMall(validatedData, decoded.userId);
        return NextResponse.json(result, { status: 201 });
    }
    catch (err) {
        return errorHandler(err);
    }
}