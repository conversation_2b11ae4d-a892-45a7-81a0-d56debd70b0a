import { error<PERSON><PERSON><PERSON>, UserR<PERSON> } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { deleteShopHour, updateShopHour } from "@mallsurf/shop-management";
import { NextRequest, NextResponse } from "next/server";

export async function PUT(request: NextRequest, { params }: { params: Promise<{ shopId: string, hourId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { hourId } = await params;
        const body = (await request.json()) as Partial<{ day: number, open: string, close: string, isClosed: boolean }>;
        const result = await updateShopHour(body, hourId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ shopId: string, hourId: string }> }) {
    try {
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin && decoded.role != UserRole.mall_manager) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const { hourId } = await params;
        const result = await deleteShopHour(hourId);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}
