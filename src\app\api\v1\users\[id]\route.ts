import { errorH<PERSON><PERSON>, UserRole } from "@mallsurf/core";
import { authMiddleware } from "@mallsurf/auth";
import { deleteUser, getUser, updateUser } from "@mallsurf/auth";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const result = await getUser(id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const body = (await request.json()) as { name: string, role: UserRole, isActive: boolean };
        const result = await updateUser(body.name, body.role, body.isActive, id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err);
    }
}

export async function DLETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const { id } = await params;
        const decoded = await authMiddleware(request);
        if (decoded.role != UserRole.admin) {
            return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
        }
        const result = await deleteUser(id);
        return NextResponse.json(result, { status: 200 });
    }
    catch (err) {
        return errorHandler(err)
    }
}