import { NextRequest } from 'next/server';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { ApiError } from '../api-error';

export const authMiddleware = async (req: NextRequest)=> {
  try {
    const cookie = req.cookies.get('accessToken');
    
    if (!cookie?.value) {
      throw new ApiError(401, 'Unauthorized - No token provided');
    }
    
    const token = cookie.value;
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    return decoded;
  } catch {
    throw new ApiError(401, 'Unauthorized - Invalid token');
  }
};