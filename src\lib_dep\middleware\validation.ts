import { ZodSchema, ZodError } from 'zod';
import { ApiError } from '../api-error';

export const validate = async (schema: ZodSchema, data: unknown) => {
    try {
        return await schema.parseAsync(data);
    } catch (error: unknown) {
        if (error instanceof ZodError) {
            throw new ApiError(400, `Validation error: ${error.errors[0].message}`);
        }
        throw error;
    }
}